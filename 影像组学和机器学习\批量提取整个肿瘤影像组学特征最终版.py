"""
简单影像组学特征提取工具
处理image和mask文件夹中的对应文件对
每个图像文件对应一个mask文件，提取单个ROI的影像组学特征

环境要求：
- Python 3.8+
- pyradiomics
- SimpleITK
- pandas
- numpy
"""

import sys
import os
import subprocess
import time
from datetime import timedelta

# 全局收集失败文件信息，便于在程序末尾统一打印
FAILED_FILES = []

def install_required_packages():
    """自动安装必需的常用包"""
    packages_info = {
        'pandas': 'pandas',
        'numpy': 'numpy',
        'matplotlib': 'matplotlib',
        'nibabel': 'nibabel'
    }

    print("=" * 50)
    print("检查并安装必需的包...")
    print("=" * 50)

    for package, import_name in packages_info.items():
        try:
            __import__(import_name)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package,
                    "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
                ])
                print(f"✅ {package} 安装完成")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                print(f"💡 请手动安装: pip install {package}")

    print("=" * 50)

try:
    install_required_packages()
except Exception as e:
    print(f"⚠️ 包安装检查出错: {e}")

try:
    import pandas as pd
    import numpy as np
    from pathlib import Path
    import logging
    print("✅ 常用包导入成功")
except ImportError as e:
    print(f"❌ 常用包导入失败: {e}")
    sys.exit(1)

# 检查并导入nibabel
try:
    import nibabel as nib
    print("✅ nibabel包导入成功")
    NIBABEL_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ nibabel未安装: {e}")
    print("💡 ��在尝试安装nibabel...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "nibabel",
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
        ])
        import nibabel as nib
        print("✅ nibabel安装并导入成功")
        NIBABEL_AVAILABLE = True
    except Exception as install_error:
        print(f"❌ nibabel安装失败: {install_error}")
        print("⚠️ 某些高级修复功能可能不可用")
        NIBABEL_AVAILABLE = False

try:
    import SimpleITK as sitk
    from radiomics import featureextractor
    print("✅ PyRadiomics相关包导入成功")
    PYRADIOMICS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ PyRadiomics相关包未安装: {e}")
    print("💡 如需使用影像组学功能，请运行: python fix_pyradiomics_installation.py")
    PYRADIOMICS_AVAILABLE = False


def setup_logging(log_file):
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def get_processed_patients(output_file):
    """
    从已有的输出文件中获取已成功处理的患者列表
    返回: set of patient_names that have been successfully processed
    """
    processed_patients = set()

    if os.path.exists(output_file):
        try:
            # 读取现有的Excel文件
            existing_df = pd.read_excel(output_file)

            if 'patient_name' in existing_df.columns and len(existing_df) > 0:
                # 检查是否有有效的特征数据（不只是基本信息列）
                info_cols = ['patient_name', 'image_file', 'mask_file']
                feature_cols = [col for col in existing_df.columns if col not in info_cols]

                if len(feature_cols) > 0:
                    # 只包含有完整特征数据的患者
                    # 检查每一行是否有足够的非空特征值
                    for _, row in existing_df.iterrows():
                        patient_name = str(row['patient_name']).strip()

                        # 检查该患者是否有足够的有效特征值
                        feature_values = [row[col] for col in feature_cols]
                        non_null_features = sum(1 for val in feature_values
                                              if pd.notna(val) and val != '' and val != 'None')

                        # 如果有至少50个有效特征值，认为是成功提取的
                        if non_null_features >= 50:
                            processed_patients.add(patient_name)

                    logging.info(f"从现有文件中找到 {len(processed_patients)} 个已成功处理的患者")
                else:
                    logging.info("现有文件中没有特征数据，将重新处理所有患者")
            else:
                logging.info("现有文件为空或格式不正确，将重新处理所有患者")

        except Exception as e:
            logging.warning(f"读取现有输出文件时出错: {str(e)}，将重新处理所有患者")
    else:
        logging.info("输出文件不存在，将处理所有患者")

    return processed_patients

def merge_with_existing_results(new_features_df, output_file):
    """
    将新提取的特征与已有结果合并
    """
    if not os.path.exists(output_file):
        return new_features_df

    try:
        # 读取现有数据
        existing_df = pd.read_excel(output_file)

        if len(existing_df) == 0:
            return new_features_df

        # 合并数据 - 新数据优先（如果患者重复）
        if len(new_features_df) > 0:
            # 移除现有数据中与新数据重复的患者
            new_patients = set(new_features_df['patient_name'].astype(str))
            existing_df_filtered = existing_df[~existing_df['patient_name'].astype(str).isin(new_patients)]

            # 合并
            merged_df = pd.concat([existing_df_filtered, new_features_df], ignore_index=True)

            logging.info(f"合并结果: 保留已有 {len(existing_df_filtered)} 个患者，新增 {len(new_features_df)} 个患者")
            return merged_df
        else:
            return existing_df

    except Exception as e:
        logging.error(f"合并现有结果时出错: {str(e)}，仅保存新结果")
        return new_features_df

def extract_patient_name_from_filename(filename):
    """从文件名中提取患者姓名"""
    name_without_ext = Path(filename).stem
    if '-' in name_without_ext:
        patient_name = name_without_ext.split('-')[0]
    else:
        patient_name = name_without_ext
    return patient_name.strip()

def match_image_and_masks(image_folder, mask_folder):
    """
    匹配image文件夹和mask文件夹中的文件对
    返回: [(patient_name, image_file, mask_file), ...]
    """
    if not os.path.exists(image_folder):
        logging.error(f"图像文件夹不存在: {image_folder}")
        return []

    if not os.path.exists(mask_folder):
        logging.error(f"Mask文件夹不存在: {mask_folder}")
        return []

    image_files = [f for f in os.listdir(image_folder)
                   if f.endswith(('.nii', '.nii.gz', '.dcm'))
                   and 'mask' not in f.lower()]

    # 接受mask文件夹中所有的NIfTI文件（有些mask文件名可能不包含'mask'，例如 '包汉清-pp-liver.nii.gz'）
    mask_files = [f for f in os.listdir(mask_folder)
                  if f.endswith(('.nii', '.nii.gz'))]

    # 创建患者姓名到文件的映射
    image_map = {}
    for img_file in image_files:
        patient_name = extract_patient_name_from_filename(img_file)
        image_map[patient_name] = img_file

    mask_map = {}
    for mask_file in mask_files:
        patient_name = extract_patient_name_from_filename(mask_file)
        mask_map[patient_name] = mask_file

    # 匹配文件对
    matched_pairs = []
    for patient_name in image_map.keys():
        if patient_name in mask_map:
            matched_pairs.append((
                patient_name,
                image_map[patient_name],
                mask_map[patient_name]
            ))
        else:
            logging.warning(f"患者 {patient_name} 有图像文件但缺少mask文件")

    # 检查有mask但没有image的情况
    for patient_name in mask_map.keys():
        if patient_name not in image_map:
            logging.warning(f"患者 {patient_name} 有mask文件但缺少图像文件")

    logging.info(f"共找到 {len(matched_pairs)} 对匹配的图像和mask文件")
    return matched_pairs

def convert_image_pixel_type_if_needed(image):
    """转换图像像素类型以确保兼容性"""
    try:
        # 获取像素类型信息
        pixel_id = image.GetPixelID()
        pixel_type_name = sitk.GetPixelIDValueAsString(pixel_id)

        # 检查是否是向量类型或者有问题的类型
        problematic_types = [
            sitk.sitkVectorFloat64,
            sitk.sitkVectorFloat32,
            sitk.sitkVectorInt32,
            sitk.sitkVectorUInt32,
            sitk.sitkVectorInt16,
            sitk.sitkVectorUInt16
        ]

        if pixel_id in problematic_types:
            logging.warning(f"检测到向量像素类型: {pixel_type_name}，转换为标量类型")

            # 获取数组数据
            array = sitk.GetArrayFromImage(image)

            # 如果是多分量向量，取第一个分量或求平均值
            if len(array.shape) > 3:
                if array.shape[-1] > 1:  # 多分量
                    logging.info(f"向量图像有{array.shape[-1]}个分量，取第一个分量")
                    array = array[..., 0]
                else:
                    # 移除单分量维度
                    array = np.squeeze(array, axis=-1)

            # 转换为合适的数据类型
            if np.all(array == array.astype(np.int32)):
                # 如果都是整数，转换为int32
                array = array.astype(np.int32)
                new_image = sitk.GetImageFromArray(array)
                new_image = sitk.Cast(new_image, sitk.sitkInt32)
            else:
                # 否则转换为float32
                array = array.astype(np.float32)
                new_image = sitk.GetImageFromArray(array)
                new_image = sitk.Cast(new_image, sitk.sitkFloat32)

            # 复制原始图像的空间信息
            new_image.CopyInformation(image)

            logging.info(f"像素类型转换完成: {pixel_type_name} -> {sitk.GetPixelIDValueAsString(new_image.GetPixelID())}")
            return new_image

        # 对于其他可能有问题的类型，尝试标准化处理
        elif pixel_id in [sitk.sitkFloat64]:
            logging.info(f"转换Float64到Float32以提高兼容性")
            converted_image = sitk.Cast(image, sitk.sitkFloat32)
            return converted_image

        else:
            # 像素类型正常，直接返回
            return image

    except Exception as e:
        logging.warning(f"像素类型转换失败: {str(e)}，返回原始图像")
        return image

def try_fix_image_orientation(image):
    """尝试修复图像方向问题，专门处理非正交方向矩阵"""
    try:
        # 获取方向矩阵
        direction = image.GetDirection()

        # 检查是否有无效的方向矩阵
        if len(direction) == 9:  # 3D图像
            # 检查方向矩阵是否为正交归一化矩阵
            import numpy as np
            direction_matrix = np.array(direction).reshape(3, 3)

            # 计算矩阵的行列式，检查是否接近1（正交矩阵）
            det = np.linalg.det(direction_matrix)

            # 检查是否为正交归一化矩阵
            # 1. 行列式应该接近±1
            # 2. 矩阵乘以其转置应该接近单位矩阵
            is_orthonormal = True

            if abs(abs(det) - 1.0) > 1e-6:
                is_orthonormal = False
                logging.warning(f"方向矩阵行列式不是±1: {det}")

            # 检查正交性：A * A^T = I
            identity_check = np.dot(direction_matrix, direction_matrix.T)
            identity_matrix = np.eye(3)
            if np.max(np.abs(identity_check - identity_matrix)) > 1e-6:
                is_orthonormal = False
                logging.warning("方向矩阵不是正交的")

            # 检查方向矩阵中是否包含NaN或Inf
            if np.any(np.isnan(direction_matrix)) or np.any(np.isinf(direction_matrix)):
                is_orthonormal = False
                logging.warning("检测到方向矩阵中包含NaN或Inf")

            # 如果不是正交归一化矩阵，重置为标准方向
            if not is_orthonormal:
                logging.warning("重置为标准方向矩阵")
                image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

        elif len(direction) == 4:  # 2D图像
            direction_matrix = np.array(direction).reshape(2, 2)
            det = np.linalg.det(direction_matrix)

            is_orthonormal = True
            if abs(abs(det) - 1.0) > 1e-6:
                is_orthonormal = False

            identity_check = np.dot(direction_matrix, direction_matrix.T)
            identity_matrix = np.eye(2)
            if np.max(np.abs(identity_check - identity_matrix)) > 1e-6:
                is_orthonormal = False

            if np.any(np.isnan(direction_matrix)) or np.any(np.isinf(direction_matrix)):
                is_orthonormal = False

            if not is_orthonormal:
                logging.warning(f"重置2D方向矩阵（行列式={det}）")
                image.SetDirection((1.0, 0.0, 0.0, 1.0))

        return image
    except Exception as e:
        logging.warning(f"修复图像方向时出错: {str(e)}，强制设置标准方向")
        # 强制设置标准方向作为fallback
        try:
            if len(image.GetDirection()) == 9:
                image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))
            elif len(image.GetDirection()) == 4:
                image.SetDirection((1.0, 0.0, 0.0, 1.0))
        except:
            pass
        return image

def robust_read_image(file_path):
    """强大的图像读取函数，能处理各种文件格式问题"""
    try:
        # 方法1: 标准读取
        image = sitk.ReadImage(file_path)
        image = try_fix_image_orientation(image)
        image = convert_image_pixel_type_if_needed(image)

        # 添加额外的数据完整性检查
        image_array = sitk.GetArrayFromImage(image)
        if np.all(np.isnan(image_array)) or image_array.size == 0:
            raise ValueError("图像数据全为NaN或为空")

        return image, None

    except Exception as first_error:
        error_str = str(first_error)

        # 专门处理非正交方向矩阵错误
        if "orthonormal direction cosines" in error_str:
            logging.warning(f"检测到非正交方向矩阵错误，尝试使用nibabel修复...")

            # 尝试使用nibabel读取并修复方向矩阵
            if NIBABEL_AVAILABLE:
                try:
                    import nibabel as nib

                    # 使用nibabel读取
                    nii_img = nib.load(file_path)
                    data = nii_img.get_fdata(dtype=np.float32)

                    # 检查数据有效性
                    if np.all(np.isnan(data)) or data.size == 0:
                        raise ValueError("nibabel读取的数据全为NaN或为空")

                    # 创建SimpleITK图像
                    image = sitk.GetImageFromArray(data)

                    # 设置spacing
                    try:
                        zooms = nii_img.header.get_zooms()[:3]
                        valid_zooms = []
                        for z in zooms:
                            if z > 0 and not np.isnan(z) and not np.isinf(z):
                                valid_zooms.append(float(z))
                            else:
                                valid_zooms.append(1.0)
                        image.SetSpacing(valid_zooms)
                    except:
                        if len(data.shape) == 3:
                            image.SetSpacing([1.0, 1.0, 1.0])
                        elif len(data.shape) == 2:
                            image.SetSpacing([1.0, 1.0])

                    # 强制设置标准正交方向矩阵
                    if len(data.shape) == 3:
                        image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))
                    elif len(data.shape) == 2:
                        image.SetDirection((1.0, 0.0, 0.0, 1.0))

                    # 设置原点
                    try:
                        affine = nii_img.affine
                        origin = affine[:3, 3]
                        image.SetOrigin([float(x) for x in origin])
                    except:
                        if len(data.shape) == 3:
                            image.SetOrigin([0.0, 0.0, 0.0])
                        elif len(data.shape) == 2:
                            image.SetOrigin([0.0, 0.0])

                    image = convert_image_pixel_type_if_needed(image)

                    logging.info(f"使用nibabel成功修复非正交方向矩阵: {file_path}")
                    return image, None

                except Exception as nibabel_error:
                    logging.warning(f"nibabel修复非正交方向矩阵失败: {str(nibabel_error)}")

        # 方法2: 使用指定的IO读取器
        io_types = ["NiftiImageIO", "MetaImageIO", "VTKImageIO", "GDCMImageIO"]
        for io_type in io_types:
            try:
                reader = sitk.ImageFileReader()
                reader.SetFileName(file_path)
                reader.SetImageIO(io_type)
                image = reader.Execute()
                image = try_fix_image_orientation(image)
                image = convert_image_pixel_type_if_needed(image)

                # 验证读取的数据
                image_array = sitk.GetArrayFromImage(image)
                if np.all(np.isnan(image_array)) or image_array.size == 0:
                    raise ValueError(f"使用{io_type}读取的数据无效")

                logging.info(f"使用{io_type}成功读取: {file_path}")
                return image, None

            except Exception as io_error:
                logging.warning(f"{io_type}读取失败: {str(io_error)}")
                continue

        # 方法3: 使用nibabel（如果可用）
        if NIBABEL_AVAILABLE:
            try:
                import nibabel as nib

                # 尝试不同的读取策略
                nii_img = nib.load(file_path)

                # 获取数据，处理可能的dtype问题
                try:
                    data = nii_img.get_fdata(dtype=np.float32)
                except:
                    data = np.array(nii_img.dataobj, dtype=np.float32)

                # 检查数据是否有效
                if np.all(np.isnan(data)) or data.size == 0:
                    raise ValueError("数据全为NaN或为空")

                # 转换为SimpleITK图像
                image = sitk.GetImageFromArray(data)

                # 设置spacing（处理可能的无效值）
                try:
                    zooms = nii_img.header.get_zooms()[:3]
                    # 检查spacing是否有效
                    valid_zooms = []
                    for z in zooms:
                        if z > 0 and not np.isnan(z) and not np.isinf(z):
                            valid_zooms.append(float(z))
                        else:
                            valid_zooms.append(1.0)  # 默认值
                    image.SetSpacing(valid_zooms)
                except:
                    # 如果无法获取spacing，使用默认值
                    if len(data.shape) == 3:
                        image.SetSpacing([1.0, 1.0, 1.0])
                    elif len(data.shape) == 2:
                        image.SetSpacing([1.0, 1.0])

                # 设置标准方向矩阵
                if len(data.shape) == 3:
                    image.SetDirection((1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))
                elif len(data.shape) == 2:
                    image.SetDirection((1.0, 0.0, 0.0, 1.0))

                # 添加像素类型转换
                image = convert_image_pixel_type_if_needed(image)

                logging.info(f"使用nibabel成功读取: {file_path}")
                return image, None

            except Exception as nibabel_error:
                logging.warning(f"nibabel读取失败: {str(nibabel_error)}")

        # 所有方法都失败
        error_msg = (f"所有读取方法都失败: "
                   f"标准读取({str(first_error)[:50]}), "
                   f"IO读取器(多种尝试失败), ")
        if NIBABEL_AVAILABLE:
            error_msg += f"nibabel方法(失败)"

        return None, error_msg

def fix_coordinate_space_mismatch(image, mask):
    """修复图像和mask的坐标空间不匹配问题"""
    try:
        # 检查空间参数
        image_origin = image.GetOrigin()
        mask_origin = mask.GetOrigin()
        image_spacing = image.GetSpacing()
        mask_spacing = mask.GetSpacing()
        image_direction = image.GetDirection()
        mask_direction = mask.GetDirection()

        # 计算坐标空间差异
        origin_diff = sum(abs(a - b) for a, b in zip(image_origin, mask_origin))
        spacing_diff = sum(abs(a - b) for a, b in zip(image_spacing, mask_spacing))

        # 更严格的阈值，避免不必要的重采样
        if origin_diff > 1e-3 or spacing_diff > 1e-3:  # 从1e-6改为1e-3，更保守
            logging.info(f"检测到坐标空间不匹配:")
            logging.info(f"  图像原点: {image_origin}, Mask原点: {mask_origin} (差异: {origin_diff})")
            logging.info(f"  图像间距: {image_spacing}, Mask间距: {mask_spacing} (差异: {spacing_diff})")

            # 检查mask是否已经与图像空间兼容
            if image.GetSize() == mask.GetSize():
                logging.info("  尺寸已匹配，尝试直接复制空间信息...")
                try:
                    # 尝试直接设置空间信息而不重采样
                    mask_copy = sitk.Image(mask)
                    mask_copy.SetOrigin(image.GetOrigin())
                    mask_copy.SetSpacing(image.GetSpacing())
                    mask_copy.SetDirection(image.GetDirection())

                    # 验证修改后的mask不为空
                    mask_array = sitk.GetArrayFromImage(mask_copy)
                    if np.sum(mask_array) > 0:
                        logging.info("✅ 通过复制空间信息完成对齐")
                        return image, mask_copy, "通过复制空间信息对齐"
                except Exception as copy_error:
                    logging.warning(f"直接复制空间信息失败: {copy_error}")

            # 如果直接复制失败，则进行重采样
            logging.info("  进行重采样对齐...")
            resampler = sitk.ResampleImageFilter()
            resampler.SetReferenceImage(image)  # 使用图像作为参考
            resampler.SetInterpolator(sitk.sitkNearestNeighbor)  # 保持标签值
            resampler.SetDefaultPixelValue(0)

            # 执行重采样
            aligned_mask = resampler.Execute(mask)

            # 验证重采样后的mask
            aligned_array = sitk.GetArrayFromImage(aligned_mask)

            if np.sum(aligned_array) == 0:
                logging.warning("⚠️ 重采样后mask变为空，保持原始mask")
                return image, mask, "重采样导致mask为空，保持原始"
            else:
                logging.info("✅ 坐标空间重采样对齐完成")
                return image, aligned_mask, "坐标空间已重采样对齐"

        return image, mask, None

    except Exception as e:
        logging.warning(f"坐标空间对齐失败: {str(e)}")
        return image, mask, f"坐标空间对齐失败: {str(e)}"

def validate_bounding_box(image, mask):
    """验证并修复mask的边界框是否在图像范围内"""
    try:
        # 获取图像大小
        image_size = image.GetSize()
        logging.info(f"  图像大小: {image_size}")

        # 获取mask的非零区域
        mask_array = sitk.GetArrayFromImage(mask)
        nonzero_indices = np.nonzero(mask_array)

        if len(nonzero_indices[0]) == 0:
            logging.warning("  ⚠️ mask在边界框验证阶段就是空的！")
            return mask, "mask为空"

        # 计算边界框 (注意SimpleITK的数组是ZYX顺序)
        z_min, z_max = int(nonzero_indices[0].min()), int(nonzero_indices[0].max())
        y_min, y_max = int(nonzero_indices[1].min()), int(nonzero_indices[1].max())
        x_min, x_max = int(nonzero_indices[2].min()), int(nonzero_indices[2].max())

        # 检查边界是否超出图像范围
        needs_fix = False
        if (x_min < 0 or x_max >= image_size[0] or
            y_min < 0 or y_max >= image_size[1] or
            z_min < 0 or z_max >= image_size[2]):
            needs_fix = True

        if needs_fix:
            logging.info(f"检测到mask边界超出图像范围:")
            logging.info(f"  图像大小: {image_size}")
            logging.info(f"  Mask边界: x({x_min},{x_max}), y({y_min},{y_max}), z({z_min},{z_max})")

            # 裁剪mask到图像范围内
            clipped_array = mask_array.copy()

            # 超出范围的区域设为0
            if z_min < 0:
                clipped_array[:abs(z_min)] = 0
            if z_max >= image_size[2]:
                clipped_array[image_size[2]:] = 0
            if y_min < 0:
                clipped_array[:, :abs(y_min)] = 0
            if y_max >= image_size[1]:
                clipped_array[:, image_size[1]:] = 0
            if x_min < 0:
                clipped_array[:, :, :abs(x_min)] = 0
            if x_max >= image_size[0]:
                clipped_array[:, :, image_size[0]:] = 0

            # 确保裁剪后的数组大小正确
            target_shape = (image_size[2], image_size[1], image_size[0])
            if clipped_array.shape != target_shape:
                # 如果大小不匹配，调整到正确大小
                final_array = np.zeros(target_shape, dtype=clipped_array.dtype)

                # 计算有效复制区域
                copy_z = min(clipped_array.shape[0], target_shape[0])
                copy_y = min(clipped_array.shape[1], target_shape[1])
                copy_x = min(clipped_array.shape[2], target_shape[2])

                final_array[:copy_z, :copy_y, :copy_x] = clipped_array[:copy_z, :copy_y, :copy_x]
                clipped_array = final_array

            # 创建新的mask图像
            clipped_mask = sitk.GetImageFromArray(clipped_array)
            clipped_mask.CopyInformation(image)  # 复制图像的空间信息

            # 验证裁剪后是否还有有效区域
            if np.sum(clipped_array) == 0:
                return mask, "裁剪后mask为空"

            logging.info("✅ mask边界已裁剪到图像范围内")
            return clipped_mask, "边界已修复"

        return mask, None

    except Exception as e:
        logging.warning(f"边界框验证失败: {str(e)}")
        return mask, f"边界框验证失败: {str(e)}"

def auto_fix_dimension_mismatch(image, mask):
    """自动修复图像和mask的维度不匹配问题"""
    try:
        # 第一步：修复坐标空间不匹配
        image, mask, coord_msg = fix_coordinate_space_mismatch(image, mask)
        if coord_msg and "失败" not in coord_msg:
            logging.info(f"坐标空间修复: {coord_msg}")

        # 第二步：验证并修复边界框
        mask, bbox_msg = validate_bounding_box(image, mask)
        if bbox_msg and "失败" not in bbox_msg and bbox_msg != "mask为空":
            logging.info(f"边界框修复: {bbox_msg}")

        image_size = image.GetSize()
        mask_size = mask.GetSize()

        # 如果尺寸完全匹配，直接返回
        if image_size == mask_size:
            return image, mask, None

        logging.info(f"检测到维度不匹配: 图像{image_size} vs mask{mask_size}")

        # 如果转置都不行，尝试重采样
        try:
            logging.info("尝试通过重采样修复尺寸不匹配...")

            # 使用图像的空间参数重采样mask
            resampler = sitk.ResampleImageFilter()
            resampler.SetReferenceImage(image)
            resampler.SetInterpolator(sitk.sitkNearestNeighbor)  # 使用最近邻插值保持标签值
            resampler.SetDefaultPixelValue(0)

            resampled_mask = resampler.Execute(mask)

            # 检查重采样后的尺寸
            resampled_size = resampled_mask.GetSize()
            if resampled_size == image_size:
                logging.info(f"✅ 通过重采样修复尺寸不匹配: {mask_size} -> {resampled_size}")
                return image, resampled_mask, "已通过重采样修复尺寸不匹配"
            else:
                logging.warning(f"重采样后仍不匹配: {resampled_size} != {image_size}")

        except Exception as resample_error:
            logging.warning(f"重采样失败: {str(resample_error)}")

        return image, mask, f"无法修复维度不匹配: 图像{image_size} vs mask{mask_size}"

    except Exception as e:
        logging.error(f"自动修复维度不匹配时出错: {str(e)}")
        return image, mask, f"修复过程出错: {str(e)}"

def validate_image_and_mask(image_path, mask_path):
    """验证图像和mask文件的有效性，包含自动修复功能"""
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            return False, f"图像文件不存在: {image_path}"
        if not os.path.exists(mask_path):
            return False, f"Mask文件不存在: {mask_path}"

        # 检查文件大小
        image_size = os.path.getsize(image_path)
        mask_size = os.path.getsize(mask_path)
        if image_size == 0:
            return False, "图像文件大小为0"
        if mask_size == 0:
            return False, "Mask文件大小为0"

        # 尝试读取文件
        image, image_error = robust_read_image(image_path)
        if image is None:
            return False, f"图像文件读取失败: {image_error}"

        mask, mask_error = robust_read_image(mask_path)
        if mask is None:
            return False, f"Mask文件读取失败: {mask_error}"

        # 检查图像维度
        image_size = image.GetSize()
        mask_size = mask.GetSize()

        if len(image_size) != 3:
            return False, f"图像维度不正确: {len(image_size)}D，期望3D"

        # 如果尺寸不匹配，尝试自动修复
        if image_size != mask_size:
            logging.warning(f"检测到尺寸不匹配，尝试自动修复...")
            fixed_image, fixed_mask, fix_message = auto_fix_dimension_mismatch(image, mask)

            # 检查修复是否成功
            if fixed_image.GetSize() != fixed_mask.GetSize():
                return False, f"尺寸修复失败: {fix_message}"
            else:
                logging.info(f"✅ 尺寸修复成功: {fix_message}")
                # 更新图像和mask引用
                image = fixed_image
                mask = fixed_mask

        # 检查mask是否为空
        mask_array = sitk.GetArrayFromImage(mask)
        if np.sum(mask_array) == 0:
            return False, "Mask文件为空（没有有效的ROI区域）"

        # 检查mask中的标签值
        unique_labels = np.unique(mask_array)
        valid_labels = [label for label in unique_labels if label > 0]
        if len(valid_labels) == 0:
            return False, "Mask中没有有效的标签值"

        return True, f"验证通过，包含标签: {valid_labels}"

    except Exception as e:
        return False, f"验证过程出错: {str(e)}"

def extract_features_for_single_roi(extractor, image_path, mask_path):
    """
    为单个ROI提取特征
    返回: (features_dict, error_message) - 成功时error_message为None
    """
    try:
        # 首先验证文件（包含自动修复功能）
        is_valid, validation_msg = validate_image_and_mask(image_path, mask_path)
        if not is_valid:
            error_msg = f"文件验证失败: {validation_msg}"
            logging.error(f"文件验证失败: {validation_msg}")
            return None, error_msg

        # 读取图像和mask
        image, image_error = robust_read_image(image_path)
        if image is None:
            error_msg = f"图像文件读取失败: {image_error}"
            logging.error(f"图像文件读取失败: {image_error}")
            return None, error_msg

        mask, mask_error = robust_read_image(mask_path)
        if mask is None:
            error_msg = f"Mask文件读取失败: {mask_error}"
            logging.error(f"Mask文件读取失败: {mask_error}")
            return None, error_msg

        # 修复图像和mask的空间匹配问题
        try:
            logging.info(f"  开始修复图像和mask的空间匹配问题...")

            # 先修复坐标空间
            image, mask, coord_msg = fix_coordinate_space_mismatch(image, mask)
            if coord_msg and "失败" not in coord_msg:
                logging.info(f"  坐标空间修复: {coord_msg}")

            # 再修复边界框
            mask, bbox_msg = validate_bounding_box(image, mask)
            if bbox_msg:
                if "失败" in bbox_msg or bbox_msg == "mask为空" or "裁剪后mask为空" in bbox_msg:
                    error_msg = f"边界框修复失败: {bbox_msg}"
                    logging.error(f"{error_msg}")
                    return None, error_msg
                else:
                    logging.info(f"  边界框修复: {bbox_msg}")

            # 检查尺寸匹配
            if image.GetSize() != mask.GetSize():
                logging.info(f"  在特征提取阶段检测到尺寸不匹配，进行维度修复...")
                image, mask, fix_message = auto_fix_dimension_mismatch(image, mask)
                if image.GetSize() != mask.GetSize():
                    error_msg = f"维度修复失败: {fix_message}"
                    logging.error(f"{error_msg}")
                    return None, error_msg
                else:
                    logging.info(f"  维度修复成功: {fix_message}")

        except Exception as fix_error:
            error_msg = f"修复过程出错: {str(fix_error)}"
            logging.error(f"{error_msg}")
            return None, error_msg

        # 验证mask的有效性
        mask_array = sitk.GetArrayFromImage(mask)
        voxel_count = np.sum(mask_array > 0)
        unique_values = np.unique(mask_array)

        # 检查是否完全为空
        if voxel_count == 0:
            error_msg = f"ROI区域为空"
            logging.warning(f"ROI区域为空")
            return None, error_msg

        # 确保mask是二值的（包含0和正值）
        if len(unique_values) == 1 and unique_values[0] == 0:
            error_msg = f"Mask全为0"
            logging.warning(f"Mask全为0")
            return None, error_msg

        # 将mask转换为二值mask（非零值设为1）
        binary_array = np.where(mask_array > 0, 1, 0).astype(np.uint8)
        binary_mask = sitk.GetImageFromArray(binary_array)
        binary_mask.CopyInformation(image)
        binary_mask = sitk.Cast(binary_mask, sitk.sitkUInt8)

        # 检查最小体素数量
        min_voxels = 3
        if voxel_count < min_voxels:
            # 尝试膨胀操作来增加区域大小
            try:
                dilate_filter = sitk.BinaryDilateImageFilter()
                dilate_filter.SetKernelRadius(1)  # 1像素半径
                dilated_mask = dilate_filter.Execute(binary_mask)

                dilated_array = sitk.GetArrayFromImage(dilated_mask)
                dilated_voxel_count = np.sum(dilated_array)

                if dilated_voxel_count >= min_voxels:
                    logging.info(f"  ROI区域通过膨胀增加到{dilated_voxel_count}个体素")
                    binary_mask = dilated_mask
                    voxel_count = dilated_voxel_count
                else:
                    error_msg = f"区域太小（仅{voxel_count}个体素，膨胀后{dilated_voxel_count}个体素）"
                    logging.warning(f"区域太小（仅{voxel_count}个体素，膨胀后{dilated_voxel_count}个体素），跳过特征提取")
                    return None, error_msg

            except Exception as dilate_error:
                error_msg = f"区域太小（仅{voxel_count}个体素），膨胀操作失败: {str(dilate_error)}"
                logging.warning(f"{error_msg}")
                return None, error_msg

        logging.info(f"  正在提取ROI特征（{voxel_count}个体素）...")

        # 执行特征提取
        try:
            # 设置更宽松的ROI要求
            original_settings = {}
            try:
                # 保存原始设置
                original_settings['minimumROIDimensions'] = extractor.settings.get('minimumROIDimensions', 2)
                original_settings['minimumROISize'] = extractor.settings.get('minimumROISize', None)

                # 设置更宽松的ROI要求
                extractor.settings['minimumROIDimensions'] = 1  # 允许1D区域
                if 'minimumROISize' in extractor.settings:
                    extractor.settings['minimumROISize'] = None  # 取消最小ROI大小限制

            except Exception as setting_error:
                logging.warning(f"修改extractor设置时出错: {setting_error}")

            # 执行特征提取
            features = extractor.execute(image, binary_mask)

            # 恢复原始设置
            try:
                if original_settings:
                    for key, value in original_settings.items():
                        if value is not None:
                            extractor.settings[key] = value
                        elif key in extractor.settings:
                            del extractor.settings[key]

            except Exception as restore_error:
                logging.warning(f"恢复extractor设置时出错: {restore_error}")

        except Exception as e:
            error_str = str(e)

            # 处理边界框错误
            if "Bounding box of ROI is larger than image space" in error_str:
                logging.warning(f"  检测到边界框超出图像空间错误，尝试修复...")

                try:
                    # 强制重新对齐mask到图像空间
                    resampler = sitk.ResampleImageFilter()
                    resampler.SetReferenceImage(image)
                    resampler.SetInterpolator(sitk.sitkNearestNeighbor)
                    resampler.SetDefaultPixelValue(0)

                    # 设置输出的空间参数与图像完全一致
                    resampler.SetOutputSpacing(image.GetSpacing())
                    resampler.SetOutputOrigin(image.GetOrigin())
                    resampler.SetOutputDirection(image.GetDirection())
                    resampler.SetSize(image.GetSize())

                    # 重新采样binary_mask
                    realigned_mask = resampler.Execute(binary_mask)

                    # 确保mask不为空
                    realigned_array = sitk.GetArrayFromImage(realigned_mask)
                    if np.sum(realigned_array) == 0:
                        # 如果重采样后为空，尝试膨胀原始mask
                        dilate_filter = sitk.BinaryDilateImageFilter()
                        dilate_filter.SetKernelRadius(1)
                        dilated_mask = dilate_filter.Execute(binary_mask)
                        realigned_mask = resampler.Execute(dilated_mask)
                        realigned_array = sitk.GetArrayFromImage(realigned_mask)

                    if np.sum(realigned_array) > 0:
                        logging.info(f"  边界框修复成功，重新尝试特征提取...")
                        features = extractor.execute(image, realigned_mask)
                        voxel_count = np.sum(realigned_array)
                        binary_mask = realigned_mask
                    else:
                        error_msg = f"边界框修复后mask为空: {error_str}"
                        return None, error_msg

                except Exception as bbox_fix_error:
                    error_msg = f"边界框修复失败: {str(bbox_fix_error)}; 原始错误: {error_str}"
                    return None, error_msg

            else:
                error_msg = f"特征提取执行失败: {error_str}"
                logging.error(f"特征提取执行失败: {error_str}")
                return None, error_msg

        # 检查特征提取结果
        if not features or len(features) == 0:
            error_msg = "特征提取结果为空"
            logging.error(f"特征提取结果为空")
            return None, error_msg

        logging.info(f"  ✓ 成功提取ROI特征")
        return features, None

    except Exception as e:
        error_msg = f"未预期错误: {str(e)}"
        logging.error(f"提取ROI特征时发生未预期错误: {str(e)}")
        return None, error_msg

def extract_all_features(image_folder, mask_folder, params_file,
                        output_file, log_file=None, resume_mode=True):
    """
    直接提取image和mask文件夹中所有匹配文件对的影像组学特征

    参数:
    image_folder: 图像文件夹路径
    mask_folder: mask文件夹路径
    params_file: PyRadiomics参数文件路径
    output_file: 输出Excel文件路径
    log_file: 日志文件路径（可选）
    resume_mode: 是否启用断点续传模式（默认True）
    """

    if not PYRADIOMICS_AVAILABLE:
        error_msg = """
❌ PyRadiomics未安装，无法执行影像组学特征提取
💡 解决方案:
   1. 运行: python fix_pyradiomics_installation.py
   2. 或手动安装: conda install -c conda-forge pyradiomics
        """
        print(error_msg)
        if log_file:
            setup_logging(log_file)
            logging.error("PyRadiomics未安装，无法执行特征提取")
        return None

    if log_file:
        setup_logging(log_file)
    else:
        logging.basicConfig(level=logging.INFO)

    logging.info("=" * 70)
    logging.info("开始影像组学特征提取（简单版）...")
    if resume_mode:
        logging.info("🔄 启用断点续传模式")
    logging.info("=" * 70)

    try:
        # 匹配文件对
        matched_pairs = match_image_and_masks(image_folder, mask_folder)

        if not matched_pairs:
            logging.error("未找到任何匹配的图像和mask文件对！")
            return None

        # 如果启用断点续传，获取已处理的患者
        processed_patients = set()
        if resume_mode:
            processed_patients = get_processed_patients(output_file)

        # 过滤出未处理的患者
        unprocessed_pairs = []
        skipped_count = 0

        for patient_name, image_file, mask_file in matched_pairs:
            if resume_mode and patient_name in processed_patients:
                skipped_count += 1
                logging.info(f"⏭️  跳过已处理患者: {patient_name}")
            else:
                unprocessed_pairs.append((patient_name, image_file, mask_file))

        if resume_mode:
            logging.info(f"📊 处理统计: 总共 {len(matched_pairs)} 个患者, 跳过 {skipped_count} 个已处理, 待处理 {len(unprocessed_pairs)} 个")

        if len(unprocessed_pairs) == 0:
            logging.info("🎉 所有患者都已处理完成，无需继续提取！")
            if os.path.exists(output_file):
                existing_df = pd.read_excel(output_file)
                logging.info(f"📄 现有结果文件包含 {len(existing_df)} 个患者的特征")
                return existing_df
            else:
                logging.warning("⚠️ 所有患者显示已处理，但输出文件不存在")
                return None

        # 创建特征提取器
        extractor = featureextractor.RadiomicsFeatureExtractor(params_file)

        # 设置更宽容的参数以处理小区域和问题文件
        try:
            # 允许更小的ROI
            extractor.settings['minimumROIDimensions'] = 1
            extractor.settings['minimumROISize'] = None

            # 设置更宽松的警告和错误处理
            extractor.settings['correctMask'] = True  # 自动修正mask
            extractor.settings['additionalInfo'] = True

            # 添加强制重采样设置
            extractor.settings['force2D'] = False
            extractor.settings['force2Ddimension'] = 0

            # 设置固定的重采样参数以确保一致性
            extractor.settings['resampledPixelSpacing'] = [2.0, 2.0, 2.0]
            extractor.settings['interpolator'] = 'sitkBSpline'
            extractor.settings['resampleBeforeExtraction'] = True

            # 如果有voxelArrayShift设置，确保它不会导致问题
            if 'voxelArrayShift' in extractor.settings:
                extractor.settings['voxelArrayShift'] = 0

            logging.info("特征提取器创建成功，已设置宽容参数")
        except Exception as e:
            logging.warning(f"设置特征提取器参数时出现警告: {str(e)}，使用默认设置")
            logging.info("特征提取器创建成功")

        features_list = []
        successful_extractions = 0
        failed_extractions = 0

        # 记录失败的患者信息
        failed_patients = []

        # 处理每对文件，显示进度
        total_pairs = len(unprocessed_pairs)
        start_time = time.time()

        for idx, (patient_name, image_file, mask_file) in enumerate(unprocessed_pairs, start=1):
            failure_reason = None
            try:
                logging.info(f"\n处理患者: {patient_name}")
                logging.info(f"  图像文件: {image_file}")
                logging.info(f"  Mask文件: {mask_file}")

                # 构建完整路径
                image_path = os.path.join(image_folder, image_file)
                mask_path = os.path.join(mask_folder, mask_file)

                # 计算进度信息
                percent = (idx / total_pairs) * 100 if total_pairs > 0 else 0
                elapsed_time = time.time() - start_time

                # 估算剩余时间
                if idx > 1:
                    avg_time_per_patient = elapsed_time / (idx - 1)
                    remaining_patients = total_pairs - idx + 1
                    eta_seconds = avg_time_per_patient * remaining_patients
                    eta_str = str(timedelta(seconds=int(eta_seconds)))
                else:
                    eta_str = "计算中..."

                # 创建进度条
                bar_length = 40
                filled_length = int(bar_length * idx // total_pairs)
                bar = '█' * filled_length + '░' * (bar_length - filled_length)

                # 在控制台打印增强的进度信息
                print(f"\n{'='*70}")
                print(f"进度: [{bar}] {percent:.1f}% ({idx}/{total_pairs})")
                print(f"当前患者: {patient_name}")
                print(f"已用时间: {str(timedelta(seconds=int(elapsed_time)))}")
                print(f"预计剩余: {eta_str}")
                print(f"成功: {successful_extractions} | 失败: {failed_extractions}")
                print(f"图像: {image_path}")
                print(f"Mask: {mask_path}")
                print(f"{'='*70}")

                # 提取特征
                features, error = extract_features_for_single_roi(
                    extractor, image_path, mask_path
                )

                if features:
                    # 初始化特征字典
                    combined_features = {
                        'patient_name': patient_name,
                        'image_file': image_file,
                        'mask_file': mask_file
                    }

                    # 添加特征
                    for key, value in features.items():
                        combined_features[key] = value

                    features_list.append(combined_features)
                    successful_extractions += 1
                    logging.info(f"  ✅ 患者 {patient_name} 特征提取完成")
                else:
                    failure_reason = error if error else "特征提取失败"
                    logging.error(f"  ❌ 患者 {patient_name} 特征提取失败: {failure_reason}")
                    failed_patients.append({
                        'patient_name': patient_name,
                        'image_file': image_file,
                        'mask_file': mask_file,
                        'failure_reason': failure_reason
                    })
                    # 保存失败文件的完整路径和错误信息
                    FAILED_FILES.append({
                        'patient_name': patient_name,
                        'image_path': os.path.join(image_folder, image_file),
                        'mask_path': os.path.join(mask_folder, mask_file),
                        'failure_reason': failure_reason
                    })
                    failed_extractions += 1

            except Exception as e:
                failure_reason = f"程序异常: {str(e)}"
                logging.error(f"  ❌ 提取患者 {patient_name} 特征时出错: {failure_reason}")
                failed_patients.append({
                    'patient_name': patient_name,
                    'image_file': image_file,
                    'mask_file': mask_file,
                    'failure_reason': failure_reason
                })
                failed_extractions += 1
                continue

        # 保存结果
        if features_list:
            new_features_df = pd.DataFrame.from_records(features_list)

            # 重新排序列
            info_cols = ['patient_name', 'image_file', 'mask_file']
            feature_cols = [col for col in new_features_df.columns if col not in info_cols]
            new_col_order = info_cols + feature_cols
            new_features_df = new_features_df[new_col_order]

            # 如果启用断点续传，合并现有结果
            if resume_mode:
                final_features_df = merge_with_existing_results(new_features_df, output_file)
                total_patients = len(final_features_df)

                logging.info(f"📊 合并后总结果: {total_patients} 个患者")
            else:
                final_features_df = new_features_df
                total_patients = len(final_features_df)

            # 保存合并后的结果到Excel
            final_features_df.to_excel(output_file, index=False)

            # 如果有失败的患者，保存失败记录
            if failed_patients:
                failed_df = pd.DataFrame(failed_patients)
                failed_output_file = output_file.replace('.xlsx', '_failed_patients.xlsx')
                failed_df.to_excel(failed_output_file, index=False)
                logging.info(f"失败患者记录已保存到: {failed_output_file}")

            logging.info("\n" + "=" * 70)
            logging.info("特征提取完成！")
            if resume_mode:
                logging.info(f"本次新提取: {successful_extractions} 个患者")
                logging.info(f"本次提取失败: {failed_extractions} 个患者")
                logging.info(f"总计患者数: {total_patients} 个")
                if skipped_count > 0:
                    logging.info(f"跳过已处理: {skipped_count} 个患者")
            else:
                logging.info(f"成功提取: {successful_extractions} 个患者")
                logging.info(f"提取失败: {failed_extractions} 个患者")
            logging.info(f"共提取了 {len(feature_cols)} 个特征列")
            logging.info(f"结果已保存到: {output_file}")
            if failed_patients:
                logging.info(f"失败记录已保存到: {failed_output_file}")
                print_all_failed_patients(failed_patients)
                # 额外打印失败文件的完整路径和错误（便于排查）
                print("\n详细失败文件列表（包含完整路径）：")
                for f in FAILED_FILES:
                    print(f"- 患者: {f['patient_name']}\n  图像: {f['image_path']}\n  Mask: {f['mask_path']}\n  错误: {f['failure_reason']}\n")
            logging.info("=" * 70)

            return final_features_df
        else:
            logging.error("没有成功提取任何患者的特征！")
            if failed_patients:
                failed_df = pd.DataFrame(failed_patients)
                failed_output_file = output_file.replace('.xlsx', '_failed_patients.xlsx')
                failed_df.to_excel(failed_output_file, index=False)
                logging.info(f"所有患者都失败了，失败记录已保存到: {failed_output_file}")
                print_all_failed_patients(failed_patients)
                # 打印详细失败文件路径
                print("\n详细失败文件列表（包含完整路径）：")
                for f in FAILED_FILES:
                    print(f"- 患者: {f['patient_name']}\n  图像: {f['image_path']}\n  Mask: {f['mask_path']}\n  错误: {f['failure_reason']}\n")
            return None

    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        raise


def print_all_failed_patients(failed_patients):
    """打印所有失败患者的详细信息"""
    if not failed_patients:
        return

    print("\n" + "=" * 80)
    print(f"📋 失败患者详细记录 (共 {len(failed_patients)} 个)")
    print("=" * 80)

    # 按失败原因分组统计
    failure_stats = {}
    for failed in failed_patients:
        reason = failed['failure_reason']
        if reason not in failure_stats:
            failure_stats[reason] = []
        failure_stats[reason].append(failed['patient_name'])

    # 打印统计信息
    print("\n📊 失败原因统计:")
    for i, (reason, patients) in enumerate(failure_stats.items(), 1):
        print(f"  {i}. {reason}: {len(patients)} 个患者")

    print(f"\n📝 详细失败列表:")
    print("-" * 80)

    for i, failed in enumerate(failed_patients, 1):
        print(f"{i:3d}. 患者: {failed['patient_name']}")
        print(f"     图像文件: {failed['image_file']}")
        print(f"     Mask文件: {failed['mask_file']}")
        print(f"     失败原因: {failed['failure_reason']}")
        print("-" * 80)

    print(f"\n💡 建议:")
    print("1. 检查失败文件是否存在或损坏")
    print("2. 确认图像和mask文件尺寸是否匹配")
    print("3. 验证mask是否包含有效的ROI区域")
    print("4. 检查文件格式是否为有效的NIfTI格式")
    print("=" * 80)


def diagnose_data_issues(image_folder, mask_folder):
    """诊断数据问题"""
    print("\n🔍 正在诊断数据问题...")

    # 获取文件列表
    image_files = [f for f in os.listdir(image_folder)
                   if f.endswith(('.nii', '.nii.gz')) and 'mask' not in f.lower()]
    # 诊断阶段也采用相同的更宽松的规则，接受所有NIfTI文件作为mask
    mask_files = [f for f in os.listdir(mask_folder)
                  if f.endswith(('.nii', '.nii.gz'))]

    print(f"图像文件数量: {len(image_files)}")
    print(f"Mask文件数量: {len(mask_files)}")

    # 匹配文件对
    matched_pairs = match_image_and_masks(image_folder, mask_folder)
    print(f"匹配的文件对数量: {len(matched_pairs)}")

    # 检查前几个文件的详细信息
    print("\n📋 检查前5个文件的详细信息:")
    issues_found = []

    for i, (patient_name, image_file, mask_file) in enumerate(matched_pairs[:5]):
        print(f"\n{i+1}. 患者: {patient_name}")
        print(f"   图像: {image_file}")
        print(f"   Mask: {mask_file}")

        image_path = os.path.join(image_folder, image_file)
        mask_path = os.path.join(mask_folder, mask_file)

        is_valid, msg = validate_image_and_mask(image_path, mask_path)
        if is_valid:
            print(f"   ✅ {msg}")
        else:
            print(f"   ❌ {msg}")
            issues_found.append((patient_name, msg))

    if issues_found:
        print(f"\n⚠️ 发现 {len(issues_found)} 个问题:")
        for patient, issue in issues_found:
            print(f"   - {patient}: {issue}")
    else:
        print("\n✅ 前5个文件检查通过")

    return len(matched_pairs), len(issues_found)

def main():
    """主函数 - 使用示例"""

    print("=" * 70)
    print("简单影像组学特征提取工具")
    print("处理image和mask文件夹中的对应文件对")
    print("✨ 支持断点续传功能：自动跳过已成功提取的患者")
    print("=" * 70)
    print("环境要求: python 3.8+, pyradiomics, SimpleITK, pandas")
    print("如遇安装问题请运行: fix_pyradiomics_installation.py")
    print("=" * 70)

    # 配置参数 - 请根据实际情况修改这些路径
    print("📁 使用预设的文件路径进行处理...")

    # 示例路径 - 请修改为您的实际路径
    image_folder = r"M:\苏大附一院NSCLC影像\nii_output\肺癌\肺窗\image\肺"
    mask_folder = r"M:\苏大附一院NSCLC影像\nii_output\肺癌\肺窗\mask\修正后的mask"
    # params_file = r"N:\肝脏MRI数据集\深度学习代码总结最新版\生境分析代码最终版\jupyterbook\pyradiomics\examples\exampleSettings\exampleMR_3mm.yaml"
    params_file = r"N:\肝脏MRI数据集\深度学习代码总结最新版\生境分析代码最终版\jupyterbook\pyradiomics\examples\exampleSettings\exampleCT.yaml"
    output_file = r"M:\苏大附一院NSCLC影像\nii_output\肺癌\肺窗\mask\修正后的mask\苏大附一院523肺癌radiomics_features.xlsx"
    log_file = r"N:\肝脏MRI数据集\HCC-EOBMRI\234HCC-nantong\extraction.log"

    print(f"📂 图像文件夹: {image_folder}")
    print(f"📂 Mask文件夹: {mask_folder}")
    print(f"⚙️ 配置文件: {params_file}")
    print(f"📄 输出文件: {output_file}")
    print(f"📋 日志文件: {log_file}")
    print("=" * 70)

    # 检查路径是否存在
    if not os.path.exists(image_folder):
        print(f"❌ 图像文件夹不存在: {image_folder}")
        print("请修改main()函数中的路径配置")
        return

    if not os.path.exists(mask_folder):
        print(f"❌ Mask文件夹不存在: {mask_folder}")
        print("请修改main()函数中的路径配置")
        return

    # 先进行数据诊断
    total_pairs, issues_count = diagnose_data_issues(image_folder, mask_folder)

    if issues_count > 0:
        print(f"\n⚠️ 发现 {issues_count} 个数据问题，但程序将继续运行并尝试处理")
        print("程序已优化，会自动跳过有问题的文件并记录失败原因")

    # 执行特征提取
    try:
        print("\n🚀 开始特征提取（启用断点续传）...")
        result_df = extract_all_features(
            image_folder=image_folder,
            mask_folder=mask_folder,
            params_file=params_file,
            output_file=output_file,
            log_file=log_file,
            resume_mode=True  # 启用断点续传
        )

        if result_df is not None:
            print(f"\n✅ 特征提取成功完成！")
            print(f"   共提取了 {len(result_df)} 个患者的特征")
            print(f"\n📁 结果文件: {output_file}")
            print(f"📋 日志文件: {log_file}")
        else:
            print("\n❌ 特征提取失败，请查看日志文件了解详情。")

        # 在主函数打印本次运行中失败的文件和错误信息（如果有）
        if FAILED_FILES:
            print("\n===== 本次运行失败的文件（含完整路径） =====")
            for f in FAILED_FILES:
                print(f"患者: {f['patient_name']}")
                print(f"  图像: {f['image_path']}")
                print(f"  Mask : {f['mask_path']}")
                print(f"  错误: {f['failure_reason']}")
                print("-----------------------------------------")

    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        print("详细错误信息请查看日志文件。")


if __name__ == "__main__":
    main()