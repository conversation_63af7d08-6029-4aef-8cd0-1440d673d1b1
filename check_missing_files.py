"""检查两个文件夹中缺少的文件"""
import os

# 定义两个文件夹路径
folder1 = r"M:\苏大附一院NSCLC影像\nii_output\肺癌\肺窗\mask\mask-prediction"
folder2 = r"M:\苏大附一院NSCLC影像\nii_output\肺癌\肺窗\mask\修正后的mask"

print("=" * 80)
print("文件夹比较分析")
print("=" * 80)

# 检查文件夹是否存在
if not os.path.exists(folder1):
    print(f"❌ 文件夹1不存在: {folder1}")
    exit(1)

if not os.path.exists(folder2):
    print(f"❌ 文件夹2不存在: {folder2}")
    exit(1)

# 获取文件列表
print("\n📂 正在读取文件列表...")
files1 = set(os.listdir(folder1))
files2 = set(os.listdir(folder2))

print(f"\n文件夹1 (mask-prediction): {len(files1)} 个文件")
print(f"文件夹2 (修正后的mask): {len(files2)} 个文件")

# 找出缺少的文件
missing_in_folder2 = files1 - files2
extra_in_folder2 = files2 - files1

print("\n" + "=" * 80)
print(f"📊 统计结果")
print("=" * 80)
print(f"文件夹2中缺少的文件数: {len(missing_in_folder2)}")
print(f"文件夹2中多出的文件数: {len(extra_in_folder2)}")

if missing_in_folder2:
    print("\n" + "=" * 80)
    print("❌ 文件夹2 (修正后的mask) 中缺少的文件:")
    print("=" * 80)
    for i, filename in enumerate(sorted(missing_in_folder2), 1):
        print(f"{i:3d}. {filename}")
    
    # 保存到文件
    output_file = "缺少的文件列表.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("文件夹2 (修正后的mask) 中缺少的文件:\n")
        f.write("=" * 80 + "\n")
        for i, filename in enumerate(sorted(missing_in_folder2), 1):
            f.write(f"{i:3d}. {filename}\n")
    print(f"\n✅ 缺少的文件列表已保存到: {output_file}")
else:
    print("\n✅ 文件夹2包含了文件夹1的所有文件!")

if extra_in_folder2:
    print("\n" + "=" * 80)
    print("➕ 文件夹2 (修正后的mask) 中多出的文件:")
    print("=" * 80)
    for i, filename in enumerate(sorted(extra_in_folder2), 1):
        print(f"{i:3d}. {filename}")

print("\n" + "=" * 80)
print("检查完成!")
print("=" * 80)

