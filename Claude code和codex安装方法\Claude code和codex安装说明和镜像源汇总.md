
🚀 快速开始
完整内容可参考使用文档

1️⃣ 安装 Node.js（已安装可跳过）
确保 Node.js 版本 ≥ 18.0


# Ubuntu / Debian 用户
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo bash -
sudo apt-get install -y nodejs
node --version

# macOS 用户
sudo xcode-select --install
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
brew install node
node --version
2️⃣ 安装 Claude Code

npm install -g @anthropic-ai/claude-code
claude --version
3️⃣ 开始使用 Claude Code
获取 API Key：在 听泉claude控制台创建 Claude Code 专用分组，获取您的专用令牌密钥

API 地址：https://cc.ioasis.xyz


# Windows PowerShell
$env:ANTHROPIC_AUTH_TOKEN="sk-你的密钥"
$env:ANTHROPIC_BASE_URL="https://cc.ioasis.xyz"

# Linux / macOS
export ANTHROPIC_AUTH_TOKEN="sk-你的密钥"
export ANTHROPIC_BASE_URL="https://cc.ioasis.xyz"
运行 Claude Code：


cd your-project-folder
claude
初次运行引导：

✅ 选择你喜欢的主题 → Enter
✅ 确认安全须知 → Enter
✅ 使用默认 Terminal 配置 → Enter
✅ 信任工作目录 → Enter
4️⃣ 安装 CodeX

npm i -g @openai/codex
codex --version
配置文件路径：~/.codex/config.toml

需要添加以下内容：


model = "gpt-5-2025-08-07"
model_reasoning_effort = "high"
disable_response_storage = true
5️⃣ 使用 CodeX
可以在终端中永久配置 API 地址和密钥：


# Linux / macOS
export OPENAI_BASE_URL="https://cc.ioasis.xyz/v1"
export OPENAI_API_KEY="粘贴 CodeX专用分组 令牌密钥"

Windows PowerShell

$env:OPENAI_BASE_URL="https://cc.ioasis.xyz/v1"
$env:OPENAI_API_KEY="粘贴 CodeX专用分组 令牌密钥"

运行 CodeX：


cd your-project-folder
codex
6️⃣ 重要提示
Claude Code 使用 Opus 模型 时，请设置 CLAUDE_CODE_MAX_OUTPUT_TOKENS = 32000
CodeX 推荐使用最新 gpt-5-2025-08-07 模型
修改配置后请重启对应工具以使配置生效


# Claude AI 助手安装文档
### 第一步：系统准备

<!-- Windows 安装方法
1. 安装 Node.js 环境
Claude Code 需要 Node.js 环境才能运行。

方法一：官网下载（推荐） -->

打开浏览器访问 https://nodejs.org/
点击 "LTS" 版本进行下载（推荐长期支持版本）
下载完成后双击 .msi 文件
按照安装向导完成安装，保持默认设置即可

<!-- 方法二：使用包管理器 -->
<!-- 如果你安装了 Chocolatey 或 Scoop，可以使用命令行安装：
# 使用 Chocolatey
choco install nodejs
# 或使用 Scoop
scoop install nodejs -->

node --version
npm --version

<!-- 2. 安装 Git Bash (Windows 必需) -->
Claude Code 在 Windows 上需要 Git Bash 环境才能正常运行。请按照以下步骤安装：

方法一：官网下载（推荐）
访问 https://git-scm.com/download/win
下载适合您系统的版本（64位或32位）
运行下载的安装程序
安装过程中保持默认设置即可
重要：确保勾选 "Git Bash Here" 选项

<!-- 方法二：使用包管理器
如果您已安装 Chocolatey 或 Scoop：
# 使用 Chocolatey
choco install git
# 或使用 Scoop
scoop install git -->

验证安装
打开 Git Bash 输入以下命令验证：
git --version

<!-- 3. 安装 Claude Code -->
<!-- 安装 Claude Code
打开 Git Bash，运行以下命令： -->

# 全局安装 Claude Code
npm install -g @anthropic-ai/claude-code


<!-- Windows 注意事项
• 必须在 Git Bash 终端中运行安装命令
• 如果遇到权限问题，以管理员身份运行 Git Bash
• 确保 Node.js 和 Git 已正确安装
验证 Claude Code 安装
安装完成后，输入以下命令检查是否安装成功： -->

claude --version
如果显示版本号，恭喜你！Claude Code 已经成功安装了。


<!-- Linux 安装方法
方法一：使用官方仓库（推荐） -->

# 添加 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
# curl -fsSL https://deb.nodesource.com/setup_22.x | sudo bash -  

# 安装 Node.js
sudo apt-get install -y nodejs

node --version
npm --version

<!-- 某些 Linux 发行版需要安装额外依赖： -->
<!-- # Ubuntu/Debian
sudo apt install build-essential
# CentOS/RHEL
sudo dnf groupinstall "Development Tools" -->

# 全局安装 Claude Code
npm install -g @anthropic-ai/claude-code

<!-- 如果遇到权限问题，可以使用 sudo： -->
sudo npm install -g @anthropic-ai/claude-code

claude --version


<!-- 卸载命令： -->
npm uninstall -g @anthropic-ai/claude-code
rm ~/.claude -r
rm ~/.claudecode -r
rm ~/.claude.json
cd ~


<!-- 方法：永久设置镜像源
编辑你的 shell 配置文件： -->
Terminal
# 对于 bash (默认)
echo 'export ANTHROPIC_BASE_URL="https://api.aicoding.sh"' >> ~/.bashrc
echo 'export ANTHROPIC_AUTH_TOKEN="你的API密钥"' >> ~/.bashrc
source ~/.bashrc

# 对于 zsh
echo 'export ANTHROPIC_BASE_URL="https://api.aicoding.sh"' >> ~/.zshrc
echo 'export ANTHROPIC_AUTH_TOKEN="你的API密钥"' >> ~/.zshrc
source ~/.zshrc


<!-- claude code国内镜像网址：
https://jkmail.vip/ ；https://new.duckcode.top/；xiaoai.io；qcode.cc -->

<!-- 设置环境变量 -->
<!-- #windows powershell端 -->
<!-- https://jkmail.vip/ -->
echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-ZYWCy0cby9N31hUKSob1hwMBfj9cbry6iknVU53HpFynRDEe' >> ~/.bash_profile
echo -e '\n export ANTHROPIC_BASE_URL=https://jkmail.vip/' >> ~/.bash_profile
echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-ZYWCy0cby9N31hUKSob1hwMBfj9cbry6iknVU53HpFynRDEe' >> ~/.bashrc
echo -e '\n export ANTHROPIC_BASE_URL=https://jkmail.vip/' >> ~/.bashrc
echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-ZYWCy0cby9N31hUKSob1hwMBfj9cbry6iknVU53HpFynRDEe' >> ~/.zshrc
echo -e '\n export ANTHROPIC_BASE_URL=https://jkmail.vip/' >> ~/.zshrc


# 设置用户级别的永久环境变量
[Environment]::SetEnvironmentVariable("ANTHROPIC_AUTH_TOKEN", "sk-QLVP8w3LJoyNZO13CEaA8l3Ow1LZpCv84QaqdSTgiZTLUew9", "User")
[Environment]::SetEnvironmentVariable("ANTHROPIC_BASE_URL", "https://new.duckcode.top", "User")

# Windows PowerShell
# 方法1：使用.NET方法（推荐）
[Environment]::SetEnvironmentVariable("ANTHROPIC_AUTH_TOKEN", "sk-sf6i86CQqB05Zbbd6un4RUlumoDs0qjN86ea7EoAp8cLzezv", "User")
[Environment]::SetEnvironmentVariable("ANTHROPIC_BASE_URL", "https://cc.ioasis.xyz", "User")

# 方法2：使用setx命令，永久的
setx ANTHROPIC_AUTH_TOKEN "sk-sf6i86CQqB05Zbbd6un4RUlumoDs0qjN86ea7EoAp8cLzezv"
setx ANTHROPIC_BASE_URL "https://cc.ioasis.xyz"

# 查看当前环境变量
echo $env:ANTHROPIC_AUTH_TOKEN
echo $env:ANTHROPIC_BASE_URL

# 重启PowerShell后再次查看，如果还能显示就是永久的

<!-- cmd终端 -->
<!-- xiaoai.io -->
setx ANTHROPIC_BASE_URL "https://api.aicoding.sh"
setx ANTHROPIC_AUTH_TOKEN "aicoding-7c3701b9ceb917bbc3b7598487703095"

setx ANTHROPIC_AUTH_TOKEN="sk-QLVP8w3LJoyNZO13CEaA8l3Ow1LZpCv84QaqdSTgiZTLUew9" 
setx ANTHROPIC_BASE_URL="https://new.duckcode.top" 


# 对于 bash (默认)
<!-- https://jkmail.vip/ -->
echo 'export ANTHROPIC_AUTH_TOKEN=sk-ZYWCy0cby9N31hUKSob1hwMBfj9cbry6iknVU53HpFynRDEe' >> ~/.bash_profile
echo 'export ANTHROPIC_BASE_URL=https://jkmail.vip/' >> ~/.bash_profile
echo 'export ANTHROPIC_AUTH_TOKEN=sk-ZYWCy0cby9N31hUKSob1hwMBfj9cbry6iknVU53HpFynRDEe' >> ~/.bashrc
echo 'export ANTHROPIC_BASE_URL=https://jkmail.vip/' >> ~/.bashrc
echo 'export ANTHROPIC_AUTH_TOKEN=sk-ZYWCy0cby9N31hUKSob1hwMBfj9cbry6iknVU53HpFynRDEe' >> ~/.zshrc
echo 'export ANTHROPIC_BASE_URL=https://jkmail.vip/' >> ~/.zshrc



<!-- xiaoai.io -->
echo 'export ANTHROPIC_BASE_URL="https://api.aicoding.sh"' >> ~/.bashrc
echo 'export ANTHROPIC_AUTH_TOKEN=aicoding-7c3701b9ceb917bbc3b7598487703095' >> ~/.bashrc
source ~/.bashrc

<!-- #duckcode -->
echo 'export ANTHROPIC_BASE_URL="https://new.duckcode.top/"' >> ~/.bashrc
echo 'export ANTHROPIC_AUTH_TOKEN=sk-QLVP8w3LJoyNZO13CEaA8l3Ow1LZpCv84QaqdSTgiZTLUew9' >> ~/.bashrc
source ~/.bashrc


 

<!-- #kimi2 -->
Kimi API KEY:sk-SOdIFbIiWLmxK5nPiFaQOrXDuTuq9HZVgMclhrK9n3u40PhF

# Linux/macOS 启动高速版 kimi-k2-turbo-preview 模型
export ANTHROPIC_BASE_URL=https://api.moonshot.cn/anthropic
export ANTHROPIC_AUTH_TOKEN=${sk-SOdIFbIiWLmxK5nPiFaQOrXDuTuq9HZVgMclhrK9n3u40PhF}
export ANTHROPIC_MODEL=kimi-k2-turbo-preview
export ANTHROPIC_SMALL_FAST_MODEL=kimi-k2-turbo-preview
claude


<!-- ### 更新 Claude AI 助手
```bash
npm update -g @anthropic-ai/claude-code
```
### 卸载 Claude AI 助手
```bash
npm uninstall -g @anthropic-ai/claude-code
```
### 完全卸载 Node.js（可选）
```bash
sudo apt-get remove --purge nodejs npm -y
sudo apt autoremove -y
sudo rm -rf ~/.npm ~/.node-gyp -->
```

<!-- 更新 Claude Code -->
npm uninstall -g @anthropic-ai/claude-code
rm ~/.claude -r
rm ~/.claudecode -r
rm ~/.claude.json
cd ~
#使用国内镜像源安装
npm install -g http://111.180.197.234:7778/install --registry=https://registry.npmmirror.com


<!-- 谷歌邮箱和Claude账号已注册成功 -->
<EMAIL><EMAIL>



<!-- 腾讯CloudBase AI CLI 使用文档 -->
https://docs.cloudbase.net/cli-v1/ai/introduce
CloudBase AI CLI 是 CloudBase 团队提供的一个基于 Node.js 的命令行工具，用于与 CloudBase AI 服务进行交互。

<!-- Mac / Linux / Windows WSL： -->
curl https://static.cloudbase.net/cli/install/install.sh -fsS | bash

<!-- 如果你使用 Windows PowerShell： -->
irm https://static.cloudbase.net/cli/install/install.ps1 | iex

#bash下激活环境
source ~/.bashrc 

echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 使用默认配置的 AI 工具
tcb ai

# 指定 AI 工具
tcb ai -a claude 
tcb ai -a codex
tcb ai -a aider
tcb ai -a qwen

<!-- 自定义配置 -->
tcb ai --setup

选择当前要配置的 AI 编程助手，选择 Claude Code
选择配置方式，选择 自配置 API KEY 和 Base URL
输入 Base URL、API KEY
最后可选择默认要使用的 AI 编程助手，后续运行 tcb ai 时即可默认打开
全部输入后即可完成配置。

配置 Claude​
参考 自定义配置 小节，其中：

Base URL 选择自定义 URL，输入 https://api.anthropic.com
API KEY 填入您的 Anthropic API KEY，可前往前往 Anthropic 获取

配置 Kimi K2
参考 自定义配置 小节，其中：

Base URL 选择预设的 Kimi Base URL
API KEY 填入您的 Kimi API KEY:sk-SOdIFbIiWLmxK5nPiFaQOrXDuTuq9HZVgMclhrK9n3u40PhF


#%%
Codex 使用教程
跟着这个教程，你可以轻松在自己的电脑上安装并使用 Codex。

1
安装 Node.js 环境
Codex 需要 Node.js 环境才能运行。

Windows 安装方法
方法一：官网下载（推荐）

打开浏览器访问 https://nodejs.org/
点击 "LTS" 版本进行下载（推荐长期支持版本）
下载完成后双击 .msi 文件
按照安装向导完成安装，保持默认设置即可

# 全局安装 Codex
npm install -g @openai/codex

node --version
npm --version

配置 Codex 环境变量
# 在 PowerShell 中创建或编辑 Profile
notepad $PROFILE

# 在打开的文件中添加以下内容：
$env:OPENAI_BASE_URL="https://api.aicoding.sh/v1"
$env:OPENAI_API_KEY= "aicoding-7c3701b9ceb917bbc3b7598487703095"

# 保存文件后重新加载 Profile
. $PROFILE


# 临时设置 Codex 环境变量
export OPENAI_BASE_URL=https://api.aicoding.sh/v1
export OPENAI_API_KEY=aicoding-7c3701b9ceb917bbc3b7598487703095
npm install -g @openai/codex
codex hi

运行 Codex
codex
或者
npx codex 