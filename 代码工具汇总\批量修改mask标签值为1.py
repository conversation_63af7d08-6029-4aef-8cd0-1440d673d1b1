import os
import nibabel as nib
import numpy as np
from tqdm import tqdm
from concurrent.futures import ProcessPoolExecutor
import time

def process_single_file_fast(args):
    """
    超快速处理单个文件的函数
    使用内存映射和优化的数据处理
    """
    file_path, old_value, new_value = args
    
    try:
        # 使用内存映射加载，避免完全加载到内存
        nii_img = nib.load(file_path)
        
        # 获取数据，但不立即加载到内存
        data = nii_img.get_fdata()
        
        # 快速检查是否包含目标值
        if old_value in data:
            # 使用numpy的高效操作
            modified = False
            
            # 直接在原数据上操作，避免复制
            mask = (data == old_value)
            if np.any(mask):
                data[mask] = new_value
                modified = True
                
                # 使用原始数据类型保存，避免类型转换
                original_dtype = nii_img.get_data_dtype()
                if original_dtype is None:
                    original_dtype = data.dtype
                
                # 创建新的NIfTI图像
                new_img = nib.Nifti1Image(data.astype(original_dtype), 
                                        nii_img.affine, nii_img.header)
                
                # 保存文件
                nib.save(new_img, file_path)
                
                return f"✓ {os.path.basename(file_path)}", "modified"
            
        return f"- {os.path.basename(file_path)} (无需修改)", "skipped"
            
    except Exception as e:
        return f"✗ {os.path.basename(file_path)}: {str(e)}", "error"

def get_file_list_fast(input_dir):
    """
    快速获取文件列表
    """
    nii_files = []
    extensions = ('.nii.gz', '.nii')
    
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.endswith(extensions):
                nii_files.append(os.path.join(root, file))
    
    return sorted(nii_files)  # 排序以便预测处理顺序

def modify_label_values_fast(input_dir, old_value=23, new_value=1, max_workers=None):
    """
    超快速批量修改NIfTI文件中的标签值
    """
    print("🚀 启动超快速标签修改模式")
    start_time = time.time()
    
    # 检查输入目录
    if not os.path.exists(input_dir):
        print(f"❌ 错误：输入目录不存在 - {input_dir}")
        return
    
    # 快速获取文件列表
    print("📁 扫描文件...")
    nii_files = get_file_list_fast(input_dir)
    
    if not nii_files:
        print(f"❌ 在目录 {input_dir} 中未找到任何NIfTI文件")
        return
    
    print(f"📊 找到 {len(nii_files)} 个NIfTI文件")
    print(f"🔄 将标签值从 {old_value} 修改为 {new_value}")
    
    # 设置最优工作进程数
    if max_workers is None:
        max_workers = min(len(nii_files), os.cpu_count())
    
    print(f"⚡ 使用 {max_workers} 个进程并行处理")
    
    # 统计信息
    modified_count = 0
    error_count = 0
    skipped_count = 0
    
    # 准备参数
    args_list = [(file_path, old_value, new_value) for file_path in nii_files]
    
    # 多进程处理
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        results = list(tqdm(
            executor.map(process_single_file_fast, args_list),
            total=len(nii_files),
            desc="🔥 处理中",
            unit="文件",
            ncols=80
        ))
    
    # 统计结果
    print("\n📋 处理结果:")
    for message, status in results:
        if status == "modified":
            modified_count += 1
            print(message)
        elif status == "error":
            error_count += 1
            print(message)
        elif status == "skipped":
            skipped_count += 1
            # 跳过的文件不显示，减少输出
    
    # 计算处理时间
    end_time = time.time()
    total_time = end_time - start_time
    
    # 输出最终统计
    print("\n" + "="*60)
    print("🎉 处理完成!")
    print(f"📊 总文件数: {len(nii_files)}")
    print(f"✅ 成功修改: {modified_count}")
    print(f"⏭️  跳过文件: {skipped_count}")
    print(f"❌ 处理错误: {error_count}")
    print(f"⏱️  总耗时: {total_time:.2f} 秒")
    print(f"🚀 平均速度: {len(nii_files)/total_time:.1f} 文件/秒")
    print("="*60)

def quick_backup(input_dir, backup_dir):
    """
    快速备份功能
    """
    import shutil
    
    if os.path.exists(backup_dir):
        print(f"⚠️  备份目录已存在: {backup_dir}")
        return False
    
    print(f"📦 快速备份到: {backup_dir}")
    start_time = time.time()
    
    try:
        shutil.copytree(input_dir, backup_dir)
        end_time = time.time()
        print(f"✅ 备份完成! 耗时: {end_time - start_time:.2f} 秒")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔥 NIfTI标签值批量修改工具 - 超快版")
    print("="*50)
    
    # 设置路径
    input_directory = r"M:\新增10附二院\nii_output\10个肺分割"
    
    # 快速备份选项
    create_backup = input("📦 是否需要快速备份原始文件？(y/n): ").lower().strip() == 'y'
    
    if create_backup:
        backup_directory = input_directory + "_backup"
        if not quick_backup(input_directory, backup_directory):
            print("❌ 备份失败，是否继续？")
            if input("继续处理？(y/n): ").lower().strip() != 'y':
                exit()
    
    # 进程数设置
    default_workers = min(os.cpu_count(), 8)  # 限制最大进程数避免过载
    max_workers = input(f"⚡ 输入进程数 (默认{default_workers}，回车使用默认): ").strip()
    max_workers = int(max_workers) if max_workers.isdigit() else default_workers
    
    # 最终确认
    print(f"\n🎯 配置确认:")
    print(f"📁 目录: {input_directory}")
    print(f"🔄 修改: 标签值 23 → 1")
    print(f"⚡ 进程数: {max_workers}")
    
    confirm = input("\n🚀 开始处理？(y/n): ").lower().strip()
    
    if confirm == 'y':
        modify_label_values_fast(input_directory, old_value=23, new_value=1, max_workers=max_workers)
    else:
        print("❌ 操作已取消")
