#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VISTA-3D 肺肿瘤自动分割工具

一键运行的批量3D NIfTI图像肺肿瘤分割工具
基于MONAI VISTA-3D预训练模型

功能特点:
- 自动安装依赖和下载模型
- 批量处理3D NIfTI图像
- 专门针对肺肿瘤分割(标签23)
- 完整的错误处理和日志记录
- 结果可视化和统计

作者: AI Assistant
日期: 2025-01-29
版本: 1.0
"""

import os
import sys
import json
import shutil
import subprocess
import logging
import time
from pathlib import Path
from typing import List, Optional, Dict, Any

# 设置环境变量解决OpenMP冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vista3d_lung_segmentation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class VISTA3DLungTumorSegmentation:
    """VISTA-3D肺肿瘤分割器"""
    
    def __init__(self, bundle_dir: str = "./bundles", device: str = "auto"):
        """
        初始化分割器
        
        Args:
            bundle_dir: Bundle存储目录
            device: 计算设备 ('auto', 'cuda', 'cpu')
        """
        self.bundle_dir = Path(bundle_dir)
        self.vista3d_dir = self.bundle_dir / "vista3d"
        self.device = self._setup_device(device)
        
        # 肺肿瘤标签ID
        self.lung_tumor_label = 23
        
        # 创建必要目录
        self.bundle_dir.mkdir(exist_ok=True)
        
        logger.info(f"初始化VISTA-3D肺肿瘤分割器")
        logger.info(f"Bundle目录: {self.bundle_dir}")
        logger.info(f"计算设备: {self.device}")
        
    def _setup_device(self, device: str) -> str:
        """设置计算设备"""
        if device == "auto":
            try:
                import torch
                device = "cuda" if torch.cuda.is_available() else "cpu"
            except ImportError:
                device = "cpu"
        return device
        
    def install_dependencies(self) -> bool:
        """安装必要依赖"""
        logger.info("🔧 安装依赖包...")
        
        dependencies = [
            "monai>=1.3.0",
            "monai[fire]",
            "monai[nibabel]",
            "torch>=1.12.0",
            "torchvision",
            "SimpleITK",
            "nibabel",
            "numpy",
            "scipy",
            "matplotlib",
            "tqdm",
            "pytorch-ignite"
        ]
        
        for dep in dependencies:
            try:
                logger.info(f"安装 {dep}...")
                subprocess.run([
                    sys.executable, "-m", "pip", "install", dep
                ], check=True, capture_output=True, text=True)
            except subprocess.CalledProcessError as e:
                logger.warning(f"安装 {dep} 失败: {e}")
                
        logger.info("✅ 依赖安装完成")
        return True
        
    def download_vista3d(self) -> bool:
        """下载VISTA-3D模型"""
        if self.vista3d_dir.exists():
            logger.info("VISTA-3D模型已存在，跳过下载")
            return True
            
        logger.info("📥 下载VISTA-3D模型...")
        
        try:
            cmd = [
                sys.executable, "-m", "monai.bundle", "download",
                "vista3d", "--bundle_dir", str(self.bundle_dir)
            ]
            
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("✅ VISTA-3D模型下载完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 模型下载失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            return False
            
    def create_lung_inference_config(self, input_dir: str, output_dir: str) -> Path:
        """创建肺肿瘤分割推理配置"""
        # 确保使用绝对路径
        input_path = Path(input_dir).resolve()
        output_path = Path(output_dir).resolve()
        bundle_root = self.vista3d_dir.resolve()

        # 获取输入文件列表
        nii_files = list(input_path.glob("*.nii.gz")) + list(input_path.glob("*.nii"))
        if not nii_files:
            raise ValueError(f"No NIfTI files found in {input_path}")

        # 为每个文件创建输入字典
        input_dicts = []
        for nii_file in nii_files:
            input_dicts.append({
                'image': str(nii_file),
                'label_prompt': [self.lung_tumor_label]
            })

        # 基于VISTA-3D原始配置结构
        config = {
            "imports": [
                "$import glob",
                "$import os",
                "$import scripts",
                "$import numpy as np",
                "$import copy",
                "$import json",
                "$import pathlib"
            ],
            "bundle_root": str(bundle_root),
            "image_key": "image",
            "output_dir": str(output_path),
            "output_ext": ".nii.gz",
            "output_dtype": "$np.uint8",
            "output_postfix": "lung_tumor_seg",
            "separate_folder": False,
            "input_dicts": input_dicts,
            "everything_labels": "$list(set([i+1 for i in range(132)]) - set([2,16,18,20,21,23,24,25,26,27,128,129,130,131,132]))",
            "metadata_path": "$@bundle_root + '/configs/metadata.json'",
            "metadata": "$json.loads(pathlib.Path(@metadata_path).read_text())",
            "labels_dict": "$@metadata['network_data_format']['outputs']['pred']['channel_def']",
            "subclass": {
                "2": [14, 5],
                "20": [28, 29, 30, 31, 32],
                "21": "$list(range(33, 57)) + list(range(63, 98)) + [114, 120, 122]"
            },
            "input_channels": 1,
            "resample_spacing": [1.5, 1.5, 1.5],
            "sw_batch_size": 1,
            "patch_size": [128, 128, 128],
            "device": f"$torch.device('{self.device}' if torch.cuda.is_available() else 'cpu')",
            "use_point_window": True,
            "network_def": "$monai.networks.nets.vista3d132(in_channels=@input_channels)",
            "network": "$@network_def.to(@device)",

            # 预处理变换
            "preprocessing_transforms": [
                {
                    "_target_": "LoadImaged",
                    "keys": "@image_key",
                    "image_only": True
                },
                {
                    "_target_": "EnsureChannelFirstd",
                    "keys": "@image_key"
                },
                {
                    "_target_": "EnsureTyped",
                    "keys": "@image_key",
                    "device": "@device",
                    "track_meta": True
                },
                {
                    "_target_": "Spacingd",
                    "keys": "@image_key",
                    "pixdim": "@resample_spacing",
                    "mode": "bilinear"
                },
                {
                    "_target_": "CropForegroundd",
                    "keys": "@image_key",
                    "allow_smaller": True,
                    "margin": 10,
                    "source_key": "@image_key"
                },
                {
                    "_target_": "monai.apps.vista3d.transforms.VistaPreTransformd",
                    "keys": "@image_key",
                    "subclass": "@subclass",
                    "labels_dict": "@labels_dict"
                },
                {
                    "_target_": "ScaleIntensityRanged",
                    "keys": "@image_key",
                    "a_min": -963.8247715525971,
                    "a_max": 1053.678477684517,
                    "b_min": 0,
                    "b_max": 1,
                    "clip": True
                },
                {
                    "_target_": "Orientationd",
                    "keys": "@image_key",
                    "axcodes": "RAS"
                },
                {
                    "_target_": "CastToTyped",
                    "keys": "@image_key",
                    "dtype": "$torch.float32"
                }
            ],
            "preprocessing": {
                "_target_": "Compose",
                "transforms": "$@preprocessing_transforms"
            },
            "dataset": {
                "_target_": "Dataset",
                "data": "@input_dicts",
                "transform": "@preprocessing"
            },
            "dataloader": {
                "_target_": "ThreadDataLoader",
                "dataset": "@dataset",
                "batch_size": 1,
                "shuffle": False,
                "num_workers": 0
            },
            "inferer": {
                "_target_": "scripts.inferer.Vista3dInferer",
                "roi_size": "@patch_size",
                "overlap": 0.3,
                "sw_batch_size": "@sw_batch_size",
                "use_point_window": "@use_point_window"
            },
            "postprocessing": {
                "_target_": "Compose",
                "transforms": [
                    {
                        "_target_": "ToDeviced",
                        "keys": "pred",
                        "device": "cpu",
                        "_disabled_": True
                    },
                    {
                        "_target_": "monai.apps.vista3d.transforms.VistaPostTransformd",
                        "keys": "pred"
                    },
                    {
                        "_target_": "Invertd",
                        "keys": "pred",
                        "transform": "$copy.deepcopy(@preprocessing)",
                        "orig_keys": "@image_key",
                        "nearest_interp": True,
                        "to_tensor": True
                    },
                    {
                        "_target_": "Lambdad",
                        "func": "$lambda x: torch.nan_to_num(x, nan=255)",
                        "keys": "pred"
                    },
                    {
                        "_target_": "SaveImaged",
                        "keys": "pred",
                        "resample": False,
                        "output_dir": "@output_dir",
                        "output_ext": "@output_ext",
                        "output_dtype": "@output_dtype",
                        "output_postfix": "@output_postfix",
                        "separate_folder": "@separate_folder"
                    }
                ]
            },
            "handlers": [
                {
                    "_target_": "StatsHandler",
                    "iteration_log": False
                }
            ],
            "checkpointloader": {
                "_target_": "CheckpointLoader",
                "load_path": "$@bundle_root + '/models/model.pt'",
                "load_dict": {
                    "model": "@network"
                },
                "map_location": "@device"
            },
            "evaluator": {
                "_target_": "scripts.evaluator.Vista3dEvaluator",
                "device": "@device",
                "val_data_loader": "@dataloader",
                "network": "@network",
                "inferer": "@inferer",
                "postprocessing": "@postprocessing",
                "val_handlers": "@handlers",
                "amp": True,
                "hyper_kwargs": {
                    "user_prompt": True,
                    "everything_labels": "@everything_labels"
                }
            },
            "initialize": [
                "$monai.utils.set_determinism(seed=123)",
                "$@checkpointloader(@evaluator)"
            ],
            "run": [
                "$@evaluator.run()"
            ]
        }

        # 保存配置文件
        config_dir = self.vista3d_dir / "configs"
        config_dir.mkdir(exist_ok=True)
        config_file = config_dir / "lung_tumor_inference.json"

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        logger.info(f"✅ 推理配置已保存: {config_file}")
        return config_file

    def create_single_file_config(self, input_file: str, output_dir: str) -> Path:
        """为单个文件创建推理配置"""
        input_path = Path(input_file).resolve()
        output_path = Path(output_dir).resolve()
        bundle_root = self.vista3d_dir.resolve()

        # 单个文件输入字典
        input_dict = {
            'image': str(input_path),
            'label_prompt': [self.lung_tumor_label]
        }

        # 基于VISTA-3D原始配置结构（简化版）
        config = {
            "imports": [
                "$import glob",
                "$import os",
                "$import scripts",
                "$import numpy as np",
                "$import copy",
                "$import json",
                "$import pathlib"
            ],
            "bundle_root": str(bundle_root),
            "image_key": "image",
            "output_dir": str(output_path),
            "output_ext": ".nii.gz",
            "output_dtype": "$np.uint8",
            "output_postfix": "lung_tumor_seg",
            "separate_folder": False,
            "input_dicts": [input_dict],
            "everything_labels": "$list(set([i+1 for i in range(132)]) - set([2,16,18,20,21,23,24,25,26,27,128,129,130,131,132]))",
            "metadata_path": "$@bundle_root + '/configs/metadata.json'",
            "metadata": "$json.loads(pathlib.Path(@metadata_path).read_text())",
            "labels_dict": "$@metadata['network_data_format']['outputs']['pred']['channel_def']",
            "subclass": {
                "2": [14, 5],
                "20": [28, 29, 30, 31, 32],
                "21": "$list(range(33, 57)) + list(range(63, 98)) + [114, 120, 122]"
            },
            "input_channels": 1,
            "resample_spacing": [1.5, 1.5, 1.5],
            "sw_batch_size": 1,
            "patch_size": [128, 128, 128],
            "device": f"$torch.device('{self.device}' if torch.cuda.is_available() else 'cpu')",
            "use_point_window": True,
            "network_def": "$monai.networks.nets.vista3d132(in_channels=@input_channels)",
            "network": "$@network_def.to(@device)",

            # 预处理变换
            "preprocessing_transforms": [
                {
                    "_target_": "LoadImaged",
                    "keys": "@image_key",
                    "image_only": True
                },
                {
                    "_target_": "EnsureChannelFirstd",
                    "keys": "@image_key"
                },
                {
                    "_target_": "EnsureTyped",
                    "keys": "@image_key",
                    "device": "@device",
                    "track_meta": True
                },
                {
                    "_target_": "Spacingd",
                    "keys": "@image_key",
                    "pixdim": "@resample_spacing",
                    "mode": "bilinear"
                },
                {
                    "_target_": "CropForegroundd",
                    "keys": "@image_key",
                    "allow_smaller": True,
                    "margin": 10,
                    "source_key": "@image_key"
                },
                {
                    "_target_": "monai.apps.vista3d.transforms.VistaPreTransformd",
                    "keys": "@image_key",
                    "subclass": "@subclass",
                    "labels_dict": "@labels_dict"
                },
                {
                    "_target_": "ScaleIntensityRanged",
                    "keys": "@image_key",
                    "a_min": -963.8247715525971,
                    "a_max": 1053.678477684517,
                    "b_min": 0,
                    "b_max": 1,
                    "clip": True
                },
                {
                    "_target_": "Orientationd",
                    "keys": "@image_key",
                    "axcodes": "RAS"
                },
                {
                    "_target_": "CastToTyped",
                    "keys": "@image_key",
                    "dtype": "$torch.float32"
                }
            ],
            "preprocessing": {
                "_target_": "Compose",
                "transforms": "$@preprocessing_transforms"
            },
            "dataset": {
                "_target_": "Dataset",
                "data": "@input_dicts",
                "transform": "@preprocessing"
            },
            "dataloader": {
                "_target_": "ThreadDataLoader",
                "dataset": "@dataset",
                "batch_size": 1,
                "shuffle": False,
                "num_workers": 0
            },
            "inferer": {
                "_target_": "scripts.inferer.Vista3dInferer",
                "roi_size": "@patch_size",
                "overlap": 0.3,
                "sw_batch_size": "@sw_batch_size",
                "use_point_window": "@use_point_window"
            },
            "postprocessing": {
                "_target_": "Compose",
                "transforms": [
                    {
                        "_target_": "ToDeviced",
                        "keys": "pred",
                        "device": "cpu",
                        "_disabled_": True
                    },
                    {
                        "_target_": "monai.apps.vista3d.transforms.VistaPostTransformd",
                        "keys": "pred"
                    },
                    {
                        "_target_": "Invertd",
                        "keys": "pred",
                        "transform": "$copy.deepcopy(@preprocessing)",
                        "orig_keys": "@image_key",
                        "nearest_interp": True,
                        "to_tensor": True
                    },
                    {
                        "_target_": "Lambdad",
                        "func": "$lambda x: torch.nan_to_num(x, nan=255)",
                        "keys": "pred"
                    },
                    {
                        "_target_": "SaveImaged",
                        "keys": "pred",
                        "resample": False,
                        "output_dir": "@output_dir",
                        "output_ext": "@output_ext",
                        "output_dtype": "@output_dtype",
                        "output_postfix": "@output_postfix",
                        "separate_folder": "@separate_folder"
                    }
                ]
            },
            "handlers": [
                {
                    "_target_": "StatsHandler",
                    "iteration_log": False
                }
            ],
            "checkpointloader": {
                "_target_": "CheckpointLoader",
                "load_path": "$@bundle_root + '/models/model.pt'",
                "load_dict": {
                    "model": "@network"
                },
                "map_location": "@device"
            },
            "evaluator": {
                "_target_": "scripts.evaluator.Vista3dEvaluator",
                "device": "@device",
                "val_data_loader": "@dataloader",
                "network": "@network",
                "inferer": "@inferer",
                "postprocessing": "@postprocessing",
                "val_handlers": "@handlers",
                "amp": True,
                "hyper_kwargs": {
                    "user_prompt": True,
                    "everything_labels": "@everything_labels"
                }
            },
            "initialize": [
                "$monai.utils.set_determinism(seed=123)",
                "$@checkpointloader(@evaluator)"
            ],
            "run": [
                "$@evaluator.run()"
            ]
        }

        # 保存单个文件配置
        config_dir = self.vista3d_dir / "configs"
        config_dir.mkdir(exist_ok=True)

        # 使用文件名创建唯一配置文件名
        file_stem = Path(input_file).stem.replace('.nii', '')
        config_file = config_dir / f"single_{file_stem}_inference.json"

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        return config_file

    def run_segmentation(self, input_dir: str, output_dir: str) -> bool:
        """运行肺肿瘤分割"""
        logger.info("🫁 开始肺肿瘤分割...")
        logger.info(f"   输入目录: {input_dir}")
        logger.info(f"   输出目录: {output_dir}")

        # 检查输入目录
        input_path = Path(input_dir)
        if not input_path.exists():
            logger.error(f"❌ 输入目录不存在: {input_dir}")
            return False

        # 查找输入文件
        nii_files = list(input_path.glob("*.nii.gz")) + list(input_path.glob("*.nii"))
        if not nii_files:
            logger.error(f"❌ 在输入目录中未找到NIfTI文件: {input_dir}")
            return False

        # 排序文件列表确保处理顺序一致
        nii_files.sort()
        logger.info(f"📁 找到 {len(nii_files)} 个NIfTI文件")

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 检查已处理的文件，跳过已存在的结果
        processed_files = []
        remaining_files = []

        for nii_file in nii_files:
            output_name = nii_file.stem.replace('.nii', '') + '_lung_tumor_seg.nii.gz'
            output_file = output_path / output_name

            if output_file.exists():
                logger.info(f"⏭️ 跳过已处理文件: {nii_file.name}")
                processed_files.append(nii_file)
            else:
                remaining_files.append(nii_file)

        if not remaining_files:
            logger.info("✅ 所有文件已处理完成")
            return True

        logger.info(f"📊 需要处理 {len(remaining_files)} 个文件 (已跳过 {len(processed_files)} 个)")

        # 逐个处理文件以避免内存问题
        success_count = 0
        for i, nii_file in enumerate(remaining_files, 1):
            logger.info(f"🔄 处理文件 {i}/{len(remaining_files)}: {nii_file.name}")

            try:
                # 为单个文件创建配置
                single_file_config = self.create_single_file_config(str(nii_file), output_dir)

                start_time = time.time()

                cmd = [
                    sys.executable, "-m", "monai.bundle", "run",
                    "--config_file", str(single_file_config.resolve())
                ]

                # 设置超时时间防止卡死
                result = subprocess.run(
                    cmd,
                    check=True,
                    capture_output=True,
                    text=True,
                    cwd=str(self.vista3d_dir.resolve()),
                    timeout=1800  # 30分钟超时
                )

                end_time = time.time()
                duration = end_time - start_time

                # 检查输出文件是否真的生成了
                expected_output = output_path / (nii_file.stem.replace('.nii', '') + '_lung_tumor_seg.nii.gz')
                if expected_output.exists():
                    success_count += 1
                    logger.info(f"✅ 文件 {nii_file.name} 处理完成 ({duration:.2f}秒)")
                    logger.info(f"📁 输出文件: {expected_output}")
                else:
                    logger.error(f"❌ 文件 {nii_file.name} 处理失败 - 输出文件未生成: {expected_output}")
                    logger.error(f"STDOUT: {result.stdout}")
                    logger.error(f"STDERR: {result.stderr}")

                # 输出处理结果信息
                if result.stdout:
                    logger.debug(f"处理输出: {result.stdout[-500:]}")  # 只显示最后500字符

                # 清理临时配置文件
                if single_file_config.exists():
                    single_file_config.unlink()

                # 强制垃圾回收释放内存
                import gc
                gc.collect()

            except subprocess.TimeoutExpired:
                logger.error(f"⏰ 文件 {nii_file.name} 处理超时")
                continue
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ 文件 {nii_file.name} 处理失败: {e}")
                if e.stderr:
                    logger.error(f"错误详情: {e.stderr}")
                continue
            except Exception as e:
                logger.error(f"❌ 文件 {nii_file.name} 处理异常: {e}")
                continue

        logger.info(f"🎯 批量处理完成: {success_count}/{len(remaining_files)} 个文件成功")
        return success_count > 0

    def diagnose_system(self) -> Dict[str, Any]:
        """诊断系统状态"""
        logger.info("🔍 诊断系统状态...")

        diagnosis = {
            "python_version": sys.version,
            "cuda_available": False,
            "gpu_count": 0,
            "memory_info": {},
            "disk_space": {},
            "bundle_status": {}
        }

        try:
            import torch
            diagnosis["cuda_available"] = torch.cuda.is_available()
            if torch.cuda.is_available():
                diagnosis["gpu_count"] = torch.cuda.device_count()
                diagnosis["gpu_name"] = torch.cuda.get_device_name(0)
                diagnosis["gpu_memory"] = {
                    "total": torch.cuda.get_device_properties(0).total_memory / 1024**3,
                    "allocated": torch.cuda.memory_allocated(0) / 1024**3,
                    "cached": torch.cuda.memory_reserved(0) / 1024**3
                }
        except ImportError:
            logger.warning("PyTorch未安装")

        # 检查内存
        try:
            import psutil
            memory = psutil.virtual_memory()
            diagnosis["memory_info"] = {
                "total_gb": memory.total / 1024**3,
                "available_gb": memory.available / 1024**3,
                "percent_used": memory.percent
            }
        except ImportError:
            logger.warning("psutil未安装，无法获取内存信息")

        # 检查磁盘空间
        try:
            import shutil
            total, used, free = shutil.disk_usage(self.bundle_dir)
            diagnosis["disk_space"] = {
                "total_gb": total / 1024**3,
                "used_gb": used / 1024**3,
                "free_gb": free / 1024**3
            }
        except Exception as e:
            logger.warning(f"无法获取磁盘信息: {e}")

        # 检查bundle状态
        diagnosis["bundle_status"] = {
            "bundle_dir_exists": self.bundle_dir.exists(),
            "vista3d_dir_exists": self.vista3d_dir.exists(),
            "model_file_exists": (self.vista3d_dir / "models" / "model.pt").exists(),
            "config_dir_exists": (self.vista3d_dir / "configs").exists()
        }

        # 输出诊断信息
        logger.info("📊 系统诊断结果:")
        logger.info(f"   Python版本: {diagnosis['python_version'][:20]}...")
        logger.info(f"   CUDA可用: {diagnosis['cuda_available']}")
        if diagnosis['cuda_available']:
            logger.info(f"   GPU数量: {diagnosis['gpu_count']}")
            logger.info(f"   GPU名称: {diagnosis.get('gpu_name', 'Unknown')}")
        if diagnosis['memory_info']:
            logger.info(f"   可用内存: {diagnosis['memory_info']['available_gb']:.1f}GB")
        if diagnosis['disk_space']:
            logger.info(f"   可用磁盘: {diagnosis['disk_space']['free_gb']:.1f}GB")
        logger.info(f"   模型文件: {'✅' if diagnosis['bundle_status']['model_file_exists'] else '❌'}")

        return diagnosis

    def validate_results(self, output_dir: str) -> Dict[str, Any]:
        """验证分割结果"""
        logger.info("🔍 验证分割结果...")
        
        output_path = Path(output_dir)
        seg_files = list(output_path.glob("*lung_tumor_seg.nii.gz"))
        
        results = {
            "total_files": len(seg_files),
            "files": [],
            "statistics": {}
        }
        
        for seg_file in seg_files:
            file_info = {
                "filename": seg_file.name,
                "size_mb": seg_file.stat().st_size / (1024 * 1024),
                "exists": seg_file.exists()
            }
            results["files"].append(file_info)
            
        logger.info(f"📊 生成了 {results['total_files']} 个分割文件")
        
        return results
        
    def setup_environment(self) -> bool:
        """设置完整环境"""
        logger.info("🚀 设置VISTA-3D环境...")
        
        # 1. 安装依赖
        if not self.install_dependencies():
            return False
            
        # 2. 下载模型
        if not self.download_vista3d():
            return False
            
        logger.info("✅ 环境设置完成")
        return True
        
    def run_complete_pipeline(self, input_dir: str, output_dir: str, with_diagnosis: bool = True) -> bool:
        """运行完整的分割流水线"""
        logger.info("🏥 启动VISTA-3D肺肿瘤分割流水线")
        logger.info("=" * 60)

        # 0. 系统诊断（可选）
        if with_diagnosis:
            diagnosis = self.diagnose_system()

            # 检查关键资源
            if diagnosis.get('memory_info', {}).get('available_gb', 0) < 4:
                logger.warning("⚠️ 可用内存不足4GB，可能影响处理大文件")

            if diagnosis.get('disk_space', {}).get('free_gb', 0) < 10:
                logger.warning("⚠️ 可用磁盘空间不足10GB，请确保有足够空间")

        # 1. 设置环境
        if not self.setup_environment():
            logger.error("❌ 环境设置失败")
            return False

        # 2. 运行分割
        if not self.run_segmentation(input_dir, output_dir):
            logger.error("❌ 分割失败")
            return False

        # 3. 验证结果
        results = self.validate_results(output_dir)

        logger.info("🎉 分割流水线完成!")
        logger.info(f"📊 成功处理 {results['total_files']} 个文件")
        logger.info(f"📁 结果保存在: {output_dir}")

        return True

def main():
    """主函数"""
    # 在这里设置输入和输出路径
    input_dir = r"M:\新增10附二院\nii_output\nii_output" # 输入目录路径
    output_dir = r"M:\新增10附二院\nii_output\10个肺分割"  # 输出目录路径
    bundle_dir = "./bundles"  # VISTA-3D模型存储目录
    device = "auto"  # 计算设备 ('auto', 'cuda', 'cpu')
    setup_only = False  # 是否仅设置环境

    logger.info("🏥 VISTA-3D肺肿瘤自动分割工具")
    logger.info(f"📁 输入目录: {input_dir}")
    logger.info(f"📁 输出目录: {output_dir}")
    logger.info(f"🔧 模型目录: {bundle_dir}")
    logger.info(f"💻 计算设备: {device}")

    # 创建分割器
    segmenter = VISTA3DLungTumorSegmentation(
        bundle_dir=bundle_dir,
        device=device
    )

    if setup_only:
        # 仅设置环境
        success = segmenter.setup_environment()
        if success:
            print("✅ 环境设置完成，可以开始使用VISTA-3D进行分割")
        else:
            print("❌ 环境设置失败")
            sys.exit(1)
    else:
        # 运行完整流水线
        success = segmenter.run_complete_pipeline(input_dir, output_dir)
        if not success:
            sys.exit(1)

if __name__ == "__main__":
    main()