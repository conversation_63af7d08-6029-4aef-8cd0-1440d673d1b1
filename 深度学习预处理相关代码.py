#%%sklearn分层抽样，根据data['VETC']划分训练集和验证集  首选
import pandas as pd
from sklearn.model_selection import train_test_split

# 将Excel文件加载到pandas DataFrame中
data = pd.read_excel(r'h:\1.HCC-VETC\734HCC\all-HCC\734hcctumor\data\train-val.xlsx')
data.columns

# 对数据进行分层抽样，将数据分成训练集和验证集
train_set,validation_set = train_test_split(data, test_size=0.3, stratify=data['PHCC'], random_state=42)#默认42

# 将训练集和验证集保存到不同的Excel文件中
# train_set.to_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\data\train5.xlsx', index=False)
# validation_set.to_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\data\val5.xlsx', index=False)

## 打印训练集和验证集中'VETC'变量的计数
print("Count in Training Set:", train_set['PHCC'].value_counts())
print("Count in Validation Set:", validation_set['PHCC'].value_counts())

#%%sklearn分层抽样，根据stratify=y划分训练集和验证集,VETC位于表格最后1列
import pandas as pd
from sklearn.model_selection import train_test_split

# 将Excel文件加载到pandas DataFrame中
data = pd.read_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/苏州VETC汇总后完整版.xlsx')

# 分离特征（X）和目标变量（y）
X = data.drop('VETC', axis=1)  # Assuming 'VETC' is the target variable
y = data['VETC']

# 对数据进行分层抽样，将数据分成训练集和验证集
X_train, X_validation, y_train, y_validation = train_test_split(X, y, test_size=0.3, stratify=y, random_state=42)

# 创建包含 'VETC' 变量的训练集和验证集
train_set = pd.concat([X_train, y_train], axis=1)
validation_set = pd.concat([X_validation, y_validation], axis=1)

# 将训练集和验证集保存到不同的 Excel 文件中
train_set.to_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/train_set.xlsx', index=False)
validation_set.to_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/validation_set.xlsx', index=False)

## 打印训练集和验证集中'VETC'变量的计数
print("Count of VETC variable in Training Set:", train_set['VETC'].value_counts())
print("Count of VETC variable in Validation Set:", validation_set['VETC'].value_counts())

#%% nii文件移动到train，val和test文件夹下的ap pp hbp子文件夹
# 请根据train.xlsx文件中的列名name，如果name与ap pp hbp文件夹的文件名中第一个- 前面的名字一致，
# 则把文件夹下的文件移动到train文件夹下的ap pp hbp中 
import os
import pandas as pd
import shutil
def move_files(data_dir, target_dir, excel_path):
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    names = df['name'].values
    
    # 定义源文件夹和目标文件夹
    source_folders = ['ap', 'pp', 'hbp']
    
    for folder in source_folders:
        source_folder = os.path.join(data_dir, folder)
        target_folder = os.path.join(target_dir, folder)
        os.makedirs(target_folder, exist_ok=True)
        
        for file_name in os.listdir(source_folder):
            if file_name.endswith('.nii.gz'):
                base_name = os.path.basename(file_name)
                name_prefix = base_name.split('-')[0]
                
                if name_prefix in names:
                    source_file = os.path.join(source_folder, file_name)
                    target_file = os.path.join(target_folder, file_name)
                    
                    shutil.move(source_file, target_file)
                    print(f"Moved {source_file} to {target_file}")

# 定义目录和Excel文件路径
data_dir = r'K:\2020-2023HCC\all-HCC\734hcctumor\tumor\val'
train_dir = r'K:\2020-2023HCC\all-HCC\734hcctumor\tumor\val\val'
train_file_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\val2.xlsx'

# 移动训练集文件
move_files(data_dir, train_dir, train_file_path)

#%% nii文件移动到train，val和test文件夹
# 根据I:\1.HCC-VETC\datasets\all-HCC-nantong\new_file_names.xlsx路径下表格中的名字name列，
# 如果与source_folder路径下的文件名name.split('-')[0]一致，则批量移动这个文件到target_folder的文件夹
import os
import shutil
import pandas as pd
# Set directory paths and file names
excel_path = r'K:\2020-2023HCC\all-HCC\734hcctumor\data\train.xlsx'  #validation_set   external_test_set
source_folder = r'K:\2020-2023HCC\all-HCC\734hcctumor\tumor1cm\hbp'
target_folder = r'K:\2020-2023HCC\all-HCC\734hcctumor\tumor1cm\hbp\train'

if not os.path.exists(target_folder):
    os.makedirs(target_folder)

 # Read data from Excel file
file_df = pd.read_excel(excel_path)
# Print the column names to check for the correct column name
print(file_df.columns)
# Assuming the column name is 'ID', change the following line accordingly
name_col = file_df['name']

 # Loop through file names and copy files to new folder
for patient_name in name_col:
    print(patient_name)
    for source_file_name in os.listdir(source_folder):
        print(source_file_name)
        if patient_name == source_file_name.split('-')[0]:
            source_file_path = os.path.join(source_folder, source_file_name)
            target_file_path = os.path.join(target_folder, source_file_name)
            shutil.move(source_file_path, target_file_path) #copy2
            break
print('Done.')

#%%把train文件夹内文件根据结局变量VETC进行分组；
# 当name列的名字与train下面文件名file_name.split('-')[0]重复时，则把文件移动到VETC值对应的分组0、1、2文件夹，
# 如果没有则创建。给出代码
import os
import shutil
import pandas as pd
 # Set directory paths and file names
excel_path = r'K:\2020-2023HCC\579hcc\clinical data\data\train30.xlsx'
source_folder = r'K:\2023大创项目\中期data\train2'
target_folder = source_folder
 # Read data from Excel file
file_df = pd.read_excel(excel_path)

 # Group files by name and VETC
groups = file_df.groupby(['name', 'VETC'])
 # Loop through groups and move files to corresponding folders
for group_name, group_df in groups:
    name, vet = group_name
    target_folder_path = os.path.join(target_folder, str(vet))
    if not os.path.exists(target_folder_path):
        os.makedirs(target_folder_path)
    for file_name in os.listdir(source_folder):
        if name ==  file_name.split('-')[0]:
            source_file_path = os.path.join(source_folder, file_name)
            target_file_path = os.path.join(target_folder_path, file_name)
            shutil.move(source_file_path, target_file_path)
            break
print('Done.')

#%%
# 1.表给中VETC列的数值如果大于0小于0.55则赋值为1，如果数值大于0.55则赋值为2
# 2.根据表中ID列数值进行分组，按照6：2：2的比例分训练，验证和测试集
# 3.根据excel表格name批量拷贝文件到trian、test

## 自动统计文件夹内.jpg格式图片的数量
import os
import csv

path = r'K:\2020-2023HCC\579hcc\578hcc\ap\ap30\validation\1'  #jpg文件夹下有好几个文件夹，每个文件夹有不同数量图片
folders = os.listdir(path)

with open(r'K:\2020-2023HCC\579hcc\578hcc\ap\ap30\validation\1\1.csv', 'w', newline='') as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(['Folder', 'Image Count'])

    for folder in folders:
        folder_path = os.path.join(path, folder)
        if os.path.isdir(folder_path):
            image_count = len([name for name in os.listdir(folder_path) if name.endswith('.jpg')])
            writer.writerow([folder, image_count])

#%%根据csv文件中患者图片数量参数num，批量进行自动平均；第一列为ID，第2列num，第3列以后为特征

import numpy as np
import pandas as pd

# 读取csv数据
# df = pd.read_csv(r"K:\2020-2023HCC\579hcc\578hcc\data\hbp\val_swin2.csv")
df = pd.read_excel(r"K:\2020-2023HCC\579hcc\578hcc\data\hbp\val_swin2.xlsx")
print(df.columns[:5])
# 筛选num列数据并转为列表
num = [int(x) for x in list(df["num"]) if not np.isnan(x)]
# 计数到第几行的开始
count = 0
# 处理第三列到最后一列
calc_df = df.iloc[:, 3:]
# 结果存放列表
total = []
# 迭代处理区间数据
for i in num:
    # 每次处理count到count+i的数据
    temp = list(calc_df.iloc[count:count + i, :].mean(axis=0))
    # 将结果存入列表中
    total.append(temp)  # 求均值
    count += i

# 将列表转为DataFrame
merge = pd.DataFrame(data=total)
# 保存到csv文件
# merge.to_csv(r"K:\2020-2023HCC\579hcc\578hcc\data\hbp\test_swin2.csv", index=False, header=True)
merge.to_excel(r"K:\2020-2023HCC\579hcc\578hcc\data\hbp\val_swin2.xlsx", index=False, header=True)
#%%
##对提取的深度学习特征按照每5行进行平均
import  pandas  as  pd
#  读取数据
data  =  pd.read_csv("D:/pythondata/test_pred_value.csv")

#  按照ID列进行分组，然后每5行求均值  “//”后面的数必须是整数
# result  =  data.groupby(data.index // 5)['ID', 'X1', 'X2'].mean()  或者median

result = data.groupby(data.index//5)[[data.columns[0]] + list(data.columns[1:])].mean()
#  将结果保存为csv文件
result.to_csv("D:/pythondata/test_median_value.csv",index=False)

#%%
##根据excel文件中的num数值，把深度学习特征的行扩大相应的倍数；
import pandas as pd

# 读取xlsx文件
df = pd.read_excel('/root/autodl-tmp/ap/shiyan.xlsx')
# 获取需要重复的次数
repeat_counts = df['num'].astype(int)
# 重复每一行
new_df = df.loc[df.index.repeat(repeat_counts)]
# 输出到新的xlsx文件
new_df.to_excel('/root/autodl-tmp/ap/shiyan2.xlsx', index=False)

# ##根据csv文件中的num数值，把深度学习特征的行扩大相应的倍数；
# 第一列ID，第2列num，第3到后面为深度学习特征；成功了
# import csv
# with open(r'K:\2020-2023HCC\579hcc\clinical data\data\shiyan.csv', 'r') as csvfile:
#      csvreader = csv.reader(csvfile)#
#     #读取整个csv文件到内存中的列表中
#      rows = [row for row in csvreader]
#     # 查找ID列的位置
#      id_index = rows[0].index('num')#
#     # 确定每行需要重复的次数
#      repeat_counts = [int(row[id_index]) for row in rows[1:] if row[id_index].isdigit()]
#      print(repeat_counts)
#      print(len(rows))#
#     # 重复每一行
#      new_rows = []
#      for i in range(1, len(rows)):
#          for j in range(repeat_counts[i-1]):
#              new_rows.append(rows[i])#
#     # 将结果输出到文件
#      with open(r'K:\2020-2023HCC\579hcc\clinical data\data\shiyan2.csv', 'w', newline='') as outfile:
#          csvwriter = csv.writer(outfile)
#          csvwriter.writerows([rows[0]])
#          csvwriter.writerows(new_rows)

#%%I:\1.HCC-VETC\datasets\HCC-suzhou2\苏州VETC汇总后完整版.xlsx
#表给中VETC列的数值如果大于0小于0.55则赋值为1，如果数值大于0.55则赋值为2.给出代码

import pandas as pd
# Load the Excel file into a pandas DataFrame
df = pd.read_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/苏州VETC汇总后完整版.xlsx')
# Get the column you want to modify
vetc_col = df['VETC']
# Modify the values in the column based on the given condition
df.loc[vetc_col > 0.55, 'VETC'] = 2
df.loc[(vetc_col > 0) & (vetc_col < 0.55), 'VETC'] = 1
# Write the modified data back to the Excel file
df.to_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/苏州VETC汇总后完整版.xlsx', index=False)

#%%根据表中ID列数值进行分组，按照6：2：2的比例分训练，验证和测试集，
# 表中VETC为结局变量，要求VETC的分类（0、1、2）对应的ID也是按照6：2：2的比例划分到训练，验证和测试集
import pandas as pd
import numpy as np
 # Load the Excel file into a pandas DataFrame
df = pd.read_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/苏州VETC汇总后完整版.xlsx')

# Define the train, validation, and test ratios
train_ratio = 0.6
val_ratio = 0.2
test_ratio = 0.2

# Group the data by ID column and get the number of unique groups
groups = df.groupby('ID').ngroups
 # Set the random seed for reproducibility
np.random.seed(123)
 # Shuffle the group indices
group_indices = np.random.permutation(groups)
 # Compute the number of groups in each set
train_groups = int(groups * train_ratio)
val_groups = int(groups * val_ratio)
test_groups = groups - train_groups - val_groups

 # Split the group indices into train, validation, and test sets
train_indices = group_indices[:train_groups]
val_indices = group_indices[train_groups:train_groups+val_groups]
test_indices = group_indices[train_groups+val_groups:]

 # Get the corresponding IDs for each set
train_ids = df['ID'].unique()[train_indices]
val_ids = df['ID'].unique()[val_indices]
test_ids = df['ID'].unique()[test_indices]
 # Split the data into train, validation, and test sets based on the ID column
train_set = df[df['ID'].isin(train_ids)]
val_set = df[df['ID'].isin(val_ids)]
test_set = df[df['ID'].isin(test_ids)]
 # Compute the number of cases with each VETC classification in each set
train_vetc_counts = train_set['VETC'].value_counts()
val_vetc_counts = val_set['VETC'].value_counts()
test_vetc_counts = test_set['VETC'].value_counts()
 # Print the number of cases with each VETC classification in each set
print('Train set VETC counts:\n', train_vetc_counts)
print('Validation set VETC counts:\n', val_vetc_counts)
print('Test set VETC counts:\n', test_vetc_counts)

 # Write the train, validation, and test sets to separate Excel files
train_set.to_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/train_set.xlsx', index=False)
val_set.to_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/val_set.xlsx', index=False)
test_set.to_excel('I:/1.HCC-VETC/datasets/HCC-suzhou2/test_set.xlsx', index=False)


#%% 批量转换nii格式的MRI图像为jpg；niipath为nii格式文件路径
import os
import numpy as np
import nibabel as nib
import matplotlib.pyplot as plt

def nii_to_image(niipath,jpgpath):
    filenames = os.listdir(niipath)  # 读取nii文件夹
    for f in filenames:
        print(f)
        # 开始读取nii文件
        img_path = os.path.join(niipath, f)
        img = nib.load(img_path)  # 读取nii
        img_fdata = img.get_fdata()  # api 已完成转换，读出来的即为CT值
        img_fdata = np.rot90(img_fdata, 3, axes=(0, 1))  # 90度旋转
        img_fdata = np.fliplr(img_fdata)  # 左右翻转
        fname = os.path.splitext(f)[0]  # 去掉后缀名
        img_f_path = os.path.join(jpgpath, fname)
        # 创建nii对应的图像的文件夹
        if not os.path.exists(img_f_path):
            os.mkdir(img_f_path)  # 新建文件夹
        # 开始转换为图像
        z = img_fdata.shape[-1]  # 图像的数量
        for i in range(z):
            slice = img_fdata[:, :, i]  # 选择哪个方向的切片都可以
            plt.imsave(os.path.join(img_f_path, '{}.jpg'.format(i)), slice, cmap='gray')

niipath = r'K:\2020-2023HCC\579hcc\578hcc\hbp\nii_maxslice'
jpgpath= r'k:\2020-2023HCC\579hcc\578hcc\hbp\jpg_maxslice'

if __name__ == '__main__':
    nii_to_image(niipath,jpgpath)
print('completed')

#%% 批量删除像素为0的图像
import os
import cv2

# path = r"H:\1.HCC-VETC\datasets\HCC-suzhou\paper-data\ppjpg2\train\0"   #路径下有多个文件夹  external_test   validation
path = '/root/autodl-tmp/hbp/jpg/validation/1'
# 遍历文件夹
for foldername in os.listdir(path):
    folder_path = os.path.join(path, foldername)
    # 判断是否是文件夹
    if os.path.isdir(folder_path):
        # 遍历文件夹内文件
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            # 判断是否是图片文件
            if filename.endswith(".jpg") or filename.endswith(".png"):
                img = cv2.imread(file_path)

                # 判断像素值是否均为0
                if not cv2.countNonZero(cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)):
                    print("Deleting:", file_path)
                    os.remove(file_path)
print("Done!")

##删除患者文件夹中像素值和低于3万的图像
from PIL import Image
import os

# 指定D文件夹路径
folder_path = r'H:\1.HCC-VETC\datasets\HCC-suzhou\paper-data\jpg\hbp\train\0'

# 遍历D文件夹下的所有文件夹
for root, dirs, files in os.walk(folder_path):
    for patient_folder in dirs:
        patient_folder_path = os.path.join(root, patient_folder)
        # 遍历患者文件夹内的所有jpg图像
        for file_name in os.listdir(patient_folder_path):
            if file_name.endswith('.jpg'):
                image_path = os.path.join(patient_folder_path, file_name)
                # 打开图像
                image = Image.open(image_path)
                # 获取图像的像素值
                pixels = list(image.getdata())
                # 计算像素值和
                pixel_sum = sum(pixels)
                # print(f"图像 {file_name} 的像素值和为: {pixel_sum}")
                # 如果像素值和低于30000，则删除图像
                if pixel_sum < 30000:
                    os.remove(image_path)
                    print(f"已删除像素值和低于30003的图像: {file_name}，所属文件夹: {patient_folder}")

#%% 删除患者文件夹最前面2张和最后面2张图片
#这段代码会先对每个患者文件夹内的图片进行升序排列，
# 然后删除排在前面2张和排在最后2张的图片。请确保指定的路径存在，
# 并且文件夹内至少有5张图片才会执行删除操作。

import os

def natural_sort_key(s):
    """
    自定义排序函数，按照文件名的数字进行升序排序
    """
    import re
    return [int(c) if c.isdigit() else c.lower() for c in re.split(r'(\d+)', s)]

def delete_images(path):
    for patient_folder in os.listdir(path):
        patient_folder_path = os.path.join(path, patient_folder)
        if os.path.isdir(patient_folder_path):
            image_files = sorted(os.listdir(patient_folder_path), key=natural_sort_key)
            if len(image_files) >= 5:
                for i in range(2):
                    os.remove(os.path.join(patient_folder_path, image_files[i]))
                for i in range(len(image_files) - 1, len(image_files) - 3, -1):
                    os.remove(os.path.join(patient_folder_path, image_files[i]))

# 指定路径
# path = r'K:\2020-2023HCC\579hcc\579hcc\ap\jpg3\train\0'
path = '/root/autodl-tmp/hbp/jpg/test/1'
delete_images(path)
print('done')

##如果图像数大于10，则删除folder_path路径下的每个患者文件夹前3张和后3张图像，
# 如果图像数小于10大于5，则删除每个患者文件夹前2张和后2张图像
# 如果图像数小于5，则删除每个患者文件夹前1张和后1张图像，如果不够删，则跳过

import os

# 指定患者文件夹所在路径
folder_path = r'H:\1.HCC-VETC\datasets\HCC-suzhou\paper-data\jpg\ap\train\1'

# 遍历所有患者文件夹
for patient_folder in os.listdir(folder_path):
    patient_folder_path = os.path.join(folder_path, patient_folder)
    # 检查是否是文件夹
    if os.path.isdir(patient_folder_path):
        # 获取患者文件夹中的所有图像文件
        image_files = os.listdir(patient_folder_path)
        # 检查图像数量是否小于3，如果小于3，则跳过删除
        if len(image_files) < 3:
            continue
        # 根据图像数量确定要删除的图像数量
        if len(image_files) > 10:
            num_to_delete = 3
        elif len(image_files) > 5:
            num_to_delete = 2
        else:
            num_to_delete = 1
        # 删除前num_to_delete张和后num_to_delete张图像
        for i in range(num_to_delete):
            # 删除前num_to_delete张图像
            os.remove(os.path.join(patient_folder_path, image_files[i]))
            # 删除后num_to_delete张图像
            os.remove(os.path.join(patient_folder_path, image_files[-(i + 1)]))

print("Done!")
#%% 保留5个层面，ROI形状，非矩形
# 已经剪切后的tumor(或mask)的最大层面以及前面2个层面和后面2个层面，
# 如果层数小于5张，则保留所有层面图像，并把缺少的层面用最大层面图代替，补齐5张图片
import os
import nibabel as nib
import numpy as np

# 指定tumor或者mask图像所在路径   图像为ii.gz格式
folder_path = r'K:\2020-2023HCC\579hcc\579hcc\ap\nii1'
out_path = r'K:\2020-2023HCC\579hcc\579hcc\ap\nii11'

# 遍历所有nii.gz文件
for file_name in os.listdir(folder_path):
    if file_name.endswith('.nii.gz'):
        file_path = os.path.join(folder_path, file_name)
        # 加载nii.gz图像
        mask_img = nib.load(file_path)
        mask_data = mask_img.get_fdata()
        # 获取层面数量
        num_slices = mask_data.shape[2]
        print(num_slices)
        # 找到最大层面的索引
        max_slice_index = None
        max_slice_area = 0
        for i in range(num_slices):
            slice_area = (mask_data[:, :, i] > 0).sum()
            if slice_area > max_slice_area:
                max_slice_area = slice_area
                max_slice_index = i

        # 确定要保留的层面索引
        if num_slices < 5:
            start_slice = 0
            end_slice = num_slices
            # 复制最大层面图像来填补缺失的层面
            max_slice = mask_data[:, :, max_slice_index]
            for i in range(num_slices, 5):
                mask_data = np.dstack((mask_data, max_slice))
        else:
            if  max_slice_index <= 1:
                start_slice = max(0, max_slice_index - 1)
                end_slice = min(num_slices, max_slice_index + 4)
            elif num_slices - (max_slice_index+1) <= 1:
                start_slice = max(0, max_slice_index - 3)
                end_slice = min(num_slices, max_slice_index + 2)
            elif num_slices - (max_slice_index+1) >= 2:
                start_slice = max(0, max_slice_index - 2)
                end_slice = min(num_slices, max_slice_index + 3)
        # 提取要保留的层面数据
        mask_data = mask_data[:, :, start_slice:end_slice]
        # 创建新的nii图像对象
        new_mask_img = nib.Nifti1Image(mask_data, mask_img.affine, mask_img.header)
        # 保存为nii.gz格式的mask图像
        save_path = os.path.join(out_path, f'{file_name[:-7]}.nii.gz')
        nib.save(new_mask_img, save_path)
#%%  保留5个层面，ROI形状，非矩形 方案2
import os
import nibabel as nib
import numpy as np

# 指定tumor或mask图像所在路径   图像为ii.gz格式
folder_path = r'K:\2020-2023HCC\579hcc\579hcc\ap\nii1'
out_path = r'K:\2020-2023HCC\579hcc\579hcc\ap\nii11'

# 遍历所有nii.gz文件
for file_name in os.listdir(folder_path):
    if file_name.endswith('.nii.gz'):
        file_path = os.path.join(folder_path, file_name)
        # 加载nii.gz图像
        mask_img = nib.load(file_path)
        mask_data = mask_img.get_fdata()
        # 获取层面数量
        num_slices = mask_data.shape[2]
        print(num_slices)
        # 找到最大层面的索引
        max_slice_index = None
        max_slice_area = 0
        for i in range(num_slices):
            slice_area = (mask_data[:, :, i] > 0).sum()
            if slice_area > max_slice_area:
                max_slice_area = slice_area
                max_slice_index = i

        # 创建一个层面索引范围，根据最大层面的位置来选择前面或后面的5张层面
        if num_slices < 5:
            slice_indices = range(num_slices)  # 如果层数小于5张，则保留所有层面
            # 复制最大层面图像来填补缺失的层面
            max_slice = mask_data[:, :, max_slice_index]
            for i in range(num_slices, 5):
                mask_data = np.dstack((mask_data, max_slice))
        else:
            if max_slice_index < 2:
                # 最大层面位于前面1-2张
                slice_indices = range(5)
            elif num_slices - max_slice_index <= 2:
                # 最大层面位于后面1-2张
                slice_indices = range(num_slices - 5, num_slices)
            else:
                # 最大层面位于中间
                slice_indices = range(max_slice_index - 2, max_slice_index + 3)

        # 提取要保留的层面数据
        mask_data = mask_data[:, :, slice_indices]
        # 创建新的nii图像对象
        new_mask_img = nib.Nifti1Image(mask_data, mask_img.affine, mask_img.header)
        # 保存为nii.gz格式的mask图像
        save_path = os.path.join(out_path, f'{file_name[:-7]}.nii.gz')
        nib.save(new_mask_img, save_path)

#%%只保留已经剪切后肿瘤区域nii图像的最大层面
import os
import nibabel as nib

# 指定tumor或者mask图像所在路径   图像为ii.gz格式
folder_path = r'k:\2020-2023HCC\579hcc\578hcc\hbp\nii'
out_path = r'K:\2020-2023HCC\579hcc\578hcc\hbp\nii_maxslice'

# 遍历所有nii.gz文件
for file_name in os.listdir(folder_path):
    if file_name.endswith('.nii.gz'):
        file_path = os.path.join(folder_path, file_name)
        # 加载nii.gz图像
        mask_img = nib.load(file_path)
        mask_data = mask_img.get_fdata()
        # 获取层面数量
        num_slices = mask_data.shape[2]
        # 找到最大层面的索引
        max_slice_index = None
        max_slice_area = 0
        for i in range(num_slices):
            slice_area = (mask_data[:, :, i] > 0).sum()
            if slice_area > max_slice_area:
                max_slice_area = slice_area
                max_slice_index = i
        # 确定要保留的层面索引
        if max_slice_index is not None:
            start_slice = max_slice_index
            end_slice = max_slice_index + 1
            # 提取要保留的层面数据
            mask_data = mask_data[:, :, start_slice:end_slice]
            # 创建新的nii图像对象
            new_mask_img = nib.Nifti1Image(mask_data, mask_img.affine, mask_img.header)
            # 保存为nii.gz格式的mask图像
            save_path = os.path.join(out_path, f'{file_name[:-7]}.nii.gz')
            nib.save(new_mask_img, save_path)
#%% 获得nii格式image最大层面图像和前后2层，输出为nii.gz格式   矩形
# 根据mask图像找到最大层面及前后2层，并获取对应的image图像的最大层面和前后2层。然后根据mask对image图像进行剪切

import os
import nibabel as nib
import numpy as np

def crop_img(img_file, mask_file, output_dir):
    # 读取图像和掩码文件
    img_nii = nib.load(img_file)
    mask_nii = nib.load(mask_file)
    # 获取图像和掩码的数据和头文件信息
    img = img_nii.get_fdata()
    mask = mask_nii.get_fdata()
    img_header = img_nii.header
    # 获取层面数量
    num_slices = mask.shape[2]
    # 找到mask最大层面的索引
    max_mask_slice_index = None
    max_mask_slice_area = 0
    for i in range(num_slices):
        slice_area = (mask[:, :, i] > 0).sum()
        if slice_area > max_mask_slice_area:
            max_mask_slice_area = slice_area
            max_mask_slice_index = i

    # 确定要保留的mask层面索引
    if num_slices > 5:
        start_mask_slice = max(0, max_mask_slice_index - 2)
        end_mask_slice = min(num_slices, max_mask_slice_index + 3)
    else:
        start_mask_slice = max_mask_slice_index
        end_mask_slice = max_mask_slice_index + 1

    # 提取要保留的mask层面数据
    mask_data = mask[:, :, start_mask_slice:end_mask_slice]
    # 找到对应的image最大层面的索引
    max_image_slice_index = max_mask_slice_index
    # 确定要保留的image层面索引
    if num_slices >= 5:
        start_image_slice = max(0, max_image_slice_index - 2)
        end_image_slice = min(num_slices, max_image_slice_index + 3)
    else:
        start_image_slice = max_image_slice_index
        end_image_slice = max_image_slice_index + 1

    # 提取要保留的image层面数据
    image_data = img[:, :, start_image_slice:end_image_slice]
    # 获取mask图像的边界坐标
    mask_indices = np.argwhere(mask_data > 0)
    min_x, min_y, min_z = np.min(mask_indices, axis=0)
    max_x, max_y, max_z = np.max(mask_indices, axis=0)

    # 根据边界坐标裁剪image图像
    cropped_img = image_data[min_x:max_x + 1, min_y:max_y + 1, min_z:max_z + 1]
    # 创建新的nii图像对象
    cropped_img_nii = nib.Nifti1Image(cropped_img, None, img_header)

    # 保存裁剪后的图像
    output_filename = os.path.join(output_dir, os.path.basename(img_file))
    nib.save(cropped_img_nii, output_filename)
    print("Saved cropped image:", output_filename)

# 设置输入和输出路径
input_dir = r'H:\1.HCC-VETC\datasets\HCC-suzhou\HCC1-345\image\ap'  # nii格式或nii.gz
mask_dir = r'H:\1.HCC-VETC\datasets\HCC-suzhou\HCC1-345\mask\ap'
output_dir = r'H:\1.HCC-VETC\datasets\HCC-suzhou\HCC1-345\tumor\nii\ap2'

# 获取文件路径列表
img_filepaths = [os.path.join(input_dir, filename) for filename in os.listdir(input_dir)]
mask_filepaths = [os.path.join(mask_dir, filename) for filename in os.listdir(mask_dir)]

# 执行批量裁剪
for i, img_file in enumerate(img_filepaths):
    mask_file = mask_filepaths[i]
    crop_img(img_file, mask_file, output_dir)
print("Done!")

#%%把excel表格第一列的中文名字批量转为拼音，
# 新增一列空白列，把拼音保持到空白列中。给出代码

import openpyxl
from pypinyin import lazy_pinyin
 # 打开Excel文件
file_path = r"M:\HCC数据集\HCC新增待整理\遗漏的HCC.xlsx"
workbook = openpyxl.load_workbook(file_path)
 # 选择第一个工作表
worksheet = workbook.worksheets[0]
 # 遍历第一列，并将中文名字转换为拼音添加到新的一列
for row in worksheet.iter_rows(min_row=1, max_row=worksheet.max_row, min_col=1, max_col=1):
    # 获取中文名字
    name = row[0].value
    if name is not None and isinstance(name, str):
        # 获取中文名字对应的拼音
        pinyin = ''.join(lazy_pinyin(name))
        # 将拼音添加到新的一列
        worksheet.cell(row=row[0].row, column=2, value=pinyin)
 # 保存Excel文件
workbook.save(file_path)

#%%提取路径下所有序列文件夹中所有文件名的患者名字，并保存到excel中
import os
import pandas as pd
from pathlib import Path

def extract_and_save_names(base_path, outpath):
    base_path = Path(base_path)
    
    if not base_path.exists():
        print(f"错误：路径不存在 - {base_path}")
        return
    
    results = {}
    
    # 遍历所有子文件夹
    for folder in base_path.iterdir():
        if folder.is_dir():
            folder_name = folder.name
            names = set()  # 使用set避免重复
            
            # 遍历文件夹中的所有文件
            for file in folder.iterdir():
                if file.is_file():
                    file_name = file.name
                    # 提取第一个"-"前面的部分
                    if '-' in file_name:
                        name_part = file_name.split('-')[0]
                    else:
                        # 如果没有"-"，取文件名（不含扩展名）
                        name_part = file.stem
                    
                    names.add(name_part)
            
            # 转换为排序后的列表
            results[folder_name] = sorted(list(names))
            print(f"处理完成: {folder_name} - 找到 {len(names)} 个唯一名字")
    
    if not results:
        print("未找到任何数据")
        return
    
    # 创建DataFrame并保存
    max_length = max(len(names) for names in results.values())
    
    df_data = {}
    for folder_name, names in results.items():
        # 用空字符串填充到相同长度
        padded_names = names + [''] * (max_length - len(names))
        df_data[folder_name] = padded_names
    
    df = pd.DataFrame(df_data)
    
    try:
        df.to_excel(outpath, index=False)
        print(f"\n✅ 成功保存到: {outpath}")
        print(f"📊 统计信息: 共处理 {len(results)} 个序列文件夹")
        
        # 显示每个文件夹的统计
        for folder, names in results.items():
            print(f"   {folder}: {len(names)} 个名字")
            
    except Exception as e:
        print(f"❌ 保存失败: {e}")

# 使用示例
if __name__ == "__main__":    
    path = r"M:\HCC数据集\HCC新增待整理\46HCC\image"
    outpath= os.path.join(path,"文件名汇总.xlsx")
    extract_and_save_names(path, outpath)
    
#%%如果excel中VETC列和MTM列的值均为0，则VETC-MTM列值记为0，
# 如果VETC列和MTM列的值均为1，则VETC-MTM列值记为3，
# 如果VETC列值为0，MTM列的值为1，则VETC-MTM列值记为1，
# 如果VETC列值为1，MTM列的值为0，则VETC-MTM列值记为2.

import pandas as pd

# 读取Excel文件
df = pd.read_excel(r'H:\1.HCC-VETC\datasets\clinical data\nantong\南通三院病例234完整版.xlsx')

# 根据规则计算VETC-MTM列的值
df['VETC-MTM'] = 0
df.loc[(df['VETC'] == 0) & (df['MTM'] == 0), 'VETC-MTM'] = 0
df.loc[(df['VETC'] == 1) & (df['MTM'] == 1), 'VETC-MTM'] = 3
df.loc[(df['VETC'] == 0) & (df['MTM'] == 1), 'VETC-MTM'] = 1
df.loc[(df['VETC'] == 1) & (df['MTM'] == 0), 'VETC-MTM'] = 2

# 保存修改后的Excel文件
df.to_excel(r'H:\1.HCC-VETC\datasets\clinical data\nantong\123.xlsx', index=False)

#%%深度学习score:从表格probablity[0.41285115 0.5871488 ]中得到类别1的概率作为深度学习score
import pandas as pd

# 假设df是你的数据框，true_label是true label列
path = r'k:\2020-2023HCC\579hcc\578hcc\data\cp\val_swin_cp.xlsx'
df = pd.read_excel(path)
df.columns
df = pd.DataFrame(df)

# 定义一个函数来提取对应的值
def extract_value(row):
    values = row['Probability'].strip('[]').split()#strip('[]')去除字符串两端的方括号，使用split()方法将字符串按空格分割成多个子字符串
    return values[1]
    # if row['True Labels'] == 1: 
    #     return values[1]
    # else:
    #     return values[1]

# 应用函数创建新列probs
df['score'] = df.apply(extract_value, axis=1)

# 显示结果
print(df)
df.to_excel(path, index=False)

#%% 合并多个模型预测概率的excel表格成一个
import os
import pandas as pd

# 设置test文件夹路径
folder_path = r'k:\2020-2023HCC\579hcc\578hcc\data\hbp\test'  # 替换为你的test文件夹路径

# 获取文件夹内所有Excel文件的文件名
file_names = [file for file in os.listdir(folder_path) if file.endswith('.xlsx')]

# 创建一个空的数据框用于存储合并后的内容
merged_df = pd.DataFrame()

# 循环读取并横向拼接Excel文件内容
for file in file_names:
    df = pd.read_excel(os.path.join(folder_path, file))
    merged_df = pd.concat([merged_df, df], axis=1)

# 保存合并后的数据框为一个新的Excel文件
merged_df.to_excel(r'k:\2020-2023HCC\579hcc\578hcc\data\hbp\ROC-test.xlsx', index=False)  # 替换为你想要保存的文件名

# %%深度学习模型auc值比较  delong test
import pandas as pd
import numpy as np
from sklearn import metrics
from scipy.stats import sem,t,norm

# 从Excel表格中读取数据
data = pd.read_excel(r'k:\2020-2023HCC\579hcc\578hcc\data\cp\ROC-test.xlsx')

# 提取label和不同模型的预测概率
labels = data.iloc[:, 1]
model_predictions = data.iloc[:, 2:]

# 计算AUC和95%置信区间
auc_values = [metrics.roc_auc_score(labels, model_predictions.iloc[:, i]) for i in range(model_predictions.shape[1])]
auc_cis = [metrics.roc_auc_score(labels, model_predictions.iloc[:, i]) for i in range(model_predictions.shape[1])]
for i, auc in enumerate(auc_values):
    std_error = sem(model_predictions.iloc[:, i])
    confidence = 0.95
    n = len(model_predictions.iloc[:, i])
    h = std_error * t.ppf((1 + confidence) / 2, n - 1)
    lower_bound = auc - h
    upper_bound = auc + h
    print(f"模型{i+1}的AUC: {auc:.3f}, 95%置信区间: ({lower_bound:.3f}, {upper_bound:.3f})")

# 自动比较所有模型的AUC
def delong_test(y_true, y_pred1, y_pred2):
    n1 = len(y_pred1)
    n2 = len(y_pred2)
    auc1 = metrics.roc_auc_score(y_true, y_pred1)
    auc2 = metrics.roc_auc_score(y_true, y_pred2)
    v = np.var(y_pred1 - y_pred2)
    z = (auc1 - auc2) / np.sqrt(v)
    p_value = 2 * (1 - norm.cdf(np.abs(z)))
    return z, p_value

# 比较所有模型的AUC
for i in range(model_predictions.shape[1]):
    for j in range(i+1, model_predictions.shape[1]):
        model1_predictions = model_predictions.iloc[:, i]
        model2_predictions = model_predictions.iloc[:, j]
        z_score, p_value = delong_test(labels, model1_predictions, model2_predictions)
        print(f"模型{i+1}与模型{j+1}的DeLong检验的p值: {p_value:.3f}")

#%%比较不同模型的IDI  给出代码
import pandas as pd
import numpy as np
from sklearn import metrics
from scipy.stats import norm

# 从Excel表格中读取数据
data = pd.read_excel(r'K:\2020-2023HCC\579hcc\578hcc\data\cp\ROC-val.xlsx')

# 提取label和不同模型的预测概率
labels = data.iloc[:, 1]  #label列
model_predictions = data.iloc[:, 2:] #不同模型预测概率列


# 计算IDI和p值
def calculate_idi(y_true, y_pred1, y_pred2):
    event_rate1 = np.mean(y_true)
    event_rate2 = np.mean(y_pred2)
    non_event_rate1 = 1 - event_rate1
    non_event_rate2 = 1 - event_rate2
    idi = (event_rate2 - event_rate1) - (non_event_rate2 - non_event_rate1)
    n = len(y_true)
    v = np.var(y_pred1 - y_pred2)
    se_idi = np.sqrt((1 / event_rate1 + 1 / non_event_rate1 + 1 / event_rate2 + 1 / non_event_rate2) * v)
    z = idi / se_idi
    p_value = 2 * (1 - norm.cdf(np.abs(z)))
    return idi, p_value

# 比较所有模型的IDI和p值
for i in range(model_predictions.shape[1]):
    for j in range(i+1, model_predictions.shape[1]):
        model1_predictions = model_predictions.iloc[:, i]
        model2_predictions = model_predictions.iloc[:, j]
        idi, p_value = calculate_idi(labels, model1_predictions, model2_predictions)
        print(f"模型{i+1}与模型{j+1}的IDI: {idi:.3f}, p值: {p_value:.3f}")

# %%计算路径K:\2020-2023HCC\579hcc\578hcc\hbp内的文件夹中图片数量，
 #并把其内的文件夹名和对应的图片数量保存到excel中
import os
import pandas as pd

folder_path = r'k:\2020-2023HCC\579hcc\578hcc\hbp\hbp30\test\1'
output_file = r'k:\2020-2023HCC\579hcc\578hcc\hbp\hbp30\test\1\image_counts1.xlsx'

# 遍历文件夹
folder_counts = []
for root, dirs, files in os.walk(folder_path):
    image_count = len([file for file in files if file.endswith('.jpg') or file.endswith('.png')])
    folder_counts.append((os.path.basename(root), image_count))

# 创建DataFrame并保存到Excel文件
df = pd.DataFrame(folder_counts, columns=['文件夹名', '图片数量'])
df.to_excel(output_file, index=False)

# 打印文件夹名和图片数量
print(df)

#%% 直接对excel表格中多张图片的深度学习score进行平均，得到每个患者的score
# excel列中的image_paths中为很多图片路径，如('/root/autodl-tmp/ap30/test/0/pengjinling-ap.nii/2.jpg')  
# 先获得路径中的pengjinling-ap.nii名字，再对名字进行计数，并根据计数对score列的值进行平均，结果保存到新的一列
import pandas as pd
import re
# 假设 df 是您的 DataFrame，且包含 'image_path' 和 'score' 列
# 读取 Excel 文件到 DataFrame
path = r'k:\2020-2023HCC\579hcc\578hcc\data\patch\test_swin_patch2.xlsx'
df = pd.read_excel(path)

# 定义一个函数，用于从路径中提取文件夹名
# def extract_folder_name(path):
#     # 分割路径，并提取所需的文件夹名
#     parts = path.split('/')
#     for part in parts:
#         if part.endswith('.nii'):
#             return part
#     return None

def extract_folder_name(path):
    # 分割路径，并提取所需的文件夹名
    parts = path.split('/')
    for part in parts:
        if re.search('[\u4e00-\u9fa5]', part):  # 使用正则表达式匹配中文字符
            return part
    return None

# 将 'val_paths' 列转换为字符串类型
df['image_paths'] = df['image_paths'].astype(str)

# 应用函数，创建新的列 'folder_name' 来存储提取的文件夹名
df['folder_name'] = df['image_paths'].apply(extract_folder_name) #Image Path

# 计算每个文件夹名对应的 score 平均值
folder_avg_score = df.groupby('folder_name')['score'].mean().reset_index(name='avg_score')

# 将平均分数合并回原始 DataFrame
df = df.merge(folder_avg_score, on='folder_name', how='left')

# 保存结果到新的 Excel 文件
df.to_excel(path, index=False)

# %% ap、pp、hbp三期联合期特征平均
import pandas as pd

# 假设 df 是您的 DataFrame，且包含 'image_path' 和 'score' 列
# 读取 Excel 文件到 DataFrame
path = r'k:\2020-2023HCC\579hcc\578hcc\data\cp\train_swin_cp2.xlsx'
df = pd.read_excel(path)

# 计算每个文件夹名对应的 score 平均值
folder_avg_score = df.groupby('folder_name')['avg_score'].mean().reset_index(name='avg_score2')

# 将平均分数合并回原始 DataFrame
df = df.merge(folder_avg_score, on='folder_name', how='left')

# 保存结果到新的 Excel 文件
df.to_excel(path, index=False)

# %% 统计路径下患者文件夹中子文件夹内的图片数量
import os
import pandas as pd

# 患者文件夹路径
patient_folder_path = r'D:\data\patch\train\1'

# 遍历患者文件夹
patient_folders = os.listdir(patient_folder_path)

# 创建一个空的DataFrame来保存结果
result_data = []

# 遍历每个患者文件夹
for patient_folder in patient_folders:
    patient_folder_fullpath = os.path.join(patient_folder_path, patient_folder)
    
    # 判断是否为文件夹
    if os.path.isdir(patient_folder_fullpath):
        # 获取子文件夹列表
        subfolders = os.listdir(patient_folder_fullpath)
        
        # 遍历每个子文件夹
        for subfolder in subfolders:
            subfolder_fullpath = os.path.join(patient_folder_fullpath, subfolder)
            
            # 统计子文件夹内的图片数量
            image_count = len(os.listdir(subfolder_fullpath))
            
            # 将结果添加到列表中
            result_data.append({'患者文件夹': patient_folder, '子文件夹': subfolder, '图片数量': image_count})

# 创建DataFrame对象
result_df = pd.DataFrame(result_data)

# 保存结果为Excel文件
result_df.to_excel(r'D:\data\patch\train\1\图片数量统计.xlsx', index=False)

# %% deepsurv
import numpy as np
from deepsurv import deep_surv
from deepsurv.datasets import load_dataset
from deepsurv.deepsurv_logger import DeepSurvLogger, TensorboardLogger

# 加载示例数据集
X_train, X_test, T_train, T_test, E_train, E_test = load_dataset('support2')

# 创建DeepSurv模型
hyperparams = {
    "n_in": X_train.shape[1],
    "learning_rate": 1e-5,
    "hidden_layers_sizes": [6, 6],
    "activation": 'tanh',
    "batch_norm": True,
    "dropout": 0.2,
    "L2_reg": 1e-4,
    "lr_decay": 0.001,
    "momentum": 0.9,
    "n_epochs": 1000,
    "seed": 123,
    "verbose": 1
}

model = deep_surv.DeepSurv(**hyperparams)

# 创建日志记录器
logger = DeepSurvLogger(log_freq=1)

# 拟合模型
model.fit(X_train, T_train, E_train, X_test, T_test, E_test, logger)

# 预测
survival_predictions = model.predict_survival(X_test)

# 输出生存概率预测
print('Survival Probabilities:', survival_predictions)

#%% 路径下为文件夹，文件夹内为nii格式文件, 请对所有文件重命名，文件名为文件夹名-文件名   给出代码
import os

# 指定文件夹路径
folder_path = r'K:\2020-2023HCC\579hcc\HCC1-344\新增2023HCC\wuyuhuan\wuyuhuan'

# 遍历文件夹内所有文件
for root, dirs, files in os.walk(folder_path):
    for file in files:
        if file.endswith('.nii.gz'):
            # 获取文件夹名作为前缀
            folder_name = os.path.basename(root)
            # 构建新文件名
            new_name = f'{folder_name}-{file}'
            # 重命名文件
            os.rename(os.path.join(root, file), os.path.join(root, new_name))

#%% 路径为\taicang\患者文件夹\序列\.nii.gz，文件夹内为nii格式文件, 请对所有文件重命名，文件名为患者文件夹名-序列文件夹名-文件名   给出代码
import os

folder_path = r'K:\2020-2023HCC\579hcc\太仓一院HCC\太仓一院HCC新\TaiCang'

for root, dirs, files in os.walk(folder_path): 
    for file in files:
        if file.endswith(".nii.gz"):
            sequence_folder = os.path.basename(root)            
            patient_folder = os.path.basename(os.path.dirname(root))           
            # print(patient_folder)
            new_name = f"{patient_folder}-{sequence_folder}-{file}"
            print(new_name)
            os.rename(os.path.join(root, file), os.path.join(root, new_name))

#%% 批量重命名，把路径下文件名中的label改为mask
import os

# 指定文件夹路径
folder_path = r'K:\2020-2023HCC\579hcc\太仓一院HCC\太仓一院HCC新\TaiCang'

# 遍历文件夹内所有文件
for file_name in os.listdir(folder_path):
    if '-' in file_name:
        new_file_name = file_name.replace('label', 'mask')
        os.rename(os.path.join(folder_path, file_name), os.path.join(folder_path, new_file_name))

#%% 路径下的文件名如baoyong-AP-AP.nii.gz用 - 之间的内容有重复的，保留其中一个，进行重命名
import os

folder_path = r'K:\2020-2023HCC\579hcc\太仓一院HCC\太仓一院HCC新\TaiCang'

for file_name in os.listdir(folder_path):
    if os.path.isfile(os.path.join(folder_path, file_name)):
        # 分割文件名和扩展名
        name_part1, extension1 = os.path.splitext(file_name)
        name_part2, extension2 = os.path.splitext(name_part1)        
        # 用"-"分割文件名部分
        parts = name_part2.split("-")
        # 去除重复部分
        unique_parts = []
        for part in parts:
            if part not in unique_parts:
                unique_parts.append(part)
        # print(unique_parts)
        # 重组文件名并加上扩展名
        new_name = "-".join(unique_parts) + ".nii.gz"
        new_name = new_name.lower()
        print(new_name)
        # 重命名文件
        os.rename(os.path.join(folder_path, file_name), os.path.join(folder_path, new_name))

#%%批量把文件夹内的格式为nii文件批量压缩为nii.gz文件
import os
import gzip
import shutil

path = r'K:\test\LITS-Challenge-Test-Data'

def compress_nii_files(folder_path):
    # 获取文件夹中的所有文件
    file_list = os.listdir(folder_path)
    for file_name in file_list:
        if file_name.endswith(".nii"):
            # 构建输入和输出文件的完整路径
            input_file = os.path.join(folder_path, file_name)
            output_file = os.path.join(folder_path, file_name + ".gz")
            # 打开输入文件并压缩为gzip格式的输出文件
            with open(input_file, 'rb') as f_in:
                with gzip.open(output_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            # 删除原始的nii文件
            os.remove(input_file)
            print(f"已压缩文件: {input_file}")
    print("压缩完成！")

# 调用函数并传入文件夹路径
compress_nii_files(path)


#%% 根据excel中的name批量复制拷贝多个序列的nii.gz文件到新的文件夹
import os
import pandas as pd
import shutil

def copy_files(data_dir, target_dir, excel_path):
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    names = df['name'].values
    
    # 定义源文件夹和目标文件夹
    source_folders = ['ap', 'pp', 'hbp']
    
    for folder in source_folders:
        source_folder = os.path.join(data_dir, folder)
        target_folder = os.path.join(target_dir, folder)
        os.makedirs(target_folder, exist_ok=True)
        
        for file_name in os.listdir(source_folder):
            if file_name.endswith('.nii.gz'):
                base_name = os.path.basename(file_name)
                name_prefix = base_name.split('-')[0]
                
                if name_prefix in names:
                    source_file = os.path.join(source_folder, file_name)
                    target_file = os.path.join(target_folder, file_name)
                    
                    shutil.copy(source_file, target_file)  # 使用 copy 代替 move
                    print(f"Copied {source_file} to {target_file}")

# 定义目录和Excel文件路径
data_dir = r'h:\1.HCC-dataset\850HCC\all-HCC\234HCC-nantong\image'
train_dir = r"H:\1.HCC-dataset\850HCC\王加俊教授课题合作\南通hcc"
train_file_path = r"H:\1.HCC-dataset\850HCC\王加俊教授课题合作\南通HCC.xlsx"

# 复制训练集文件
copy_files(data_dir, train_dir, train_file_path)

#%% 表格1和表格2根据相同的"姓名"进行合并,横向合并
#如果表格1中的姓名与表格2中的姓名相同，则复制表格1的DFS列的内容到表格2中的PFS列。            
import pandas as pd

#读取HCC生存资料随访表整理后.xlsx和HCC临床资料1-578完整版预后.xlsx中的数据
data1 = pd.read_excel(r"M:\HCC数据集\2024.8.1-2025.5.xls")
data2 = pd.read_excel(r"M:\HCC数据集\2024.8-2025.5.30MR.xls")

#将姓名相同的行合并，左连接，以第一个数据框的name为基准，保留所有的行，横向合并
# merged_df = pd.merge(data1, data2, on='name', how='left')

#将姓名相同的行合并，只包含姓名相同的行
# merged_df = pd.merge(data1, data2, on='name', how='inner')#内连接，只保留两个数据框中都存在的行
merged_df = pd.merge(data1, data2, on='姓名', how='inner')#outer 外连接，保留两个数据框中所有的行

#保存结果到新的Excel文件
merged_df.to_excel(r"M:\HCC数据集\2024.8-2025.5.30MR-病理.xlsx", index=False)

#%%纵向合并，两个数据框中相同列名的数据合并在一列中,纵向合并
import pandas as pd

# 读取HCC生存资料随访表整理后.xlsx和HCC临床资料1-578完整版预后.xlsx中的数据
data1 = pd.read_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\data\radiomics模型结果\train_clinical_radiomics_AIC_BIC后.xlsx')
data2 = pd.read_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\data\radiomics模型结果\val_clinical_radiomics.xlsx')

# 合并两个数据框，保留所有列
merged_df = pd.concat([data1, data2], axis=0, join='outer')

# # # 找出两个数据框共同的列名,按照共同列名合并数据框
# common_columns = list(set(data1.columns) & set(data2.columns))
# print(common_columns)
# merged_df = pd.concat([data1[common_columns], data2[common_columns]], axis=0,join='inner')

# 保存结果到新的Excel文件
merged_df.to_excel(r'K:\2020-2023HCC\all-HCC\734hcctumor\1\734hbp.xlsx', index=False)

#%% 找出文件夹下文件名-前面的名字与excel中的name列不同的name，并打印出来，
import os
import pandas as pd

# 设置路径和 Excel 文件路径
directory_path = r"H:\1.HCC-dataset\850HCC\王加俊教授课题合作\mask\ap"
excel_file_path = r"H:\1.HCC-dataset\850HCC\王加俊教授课题合作\502HCC临床资料.xlsx"

# 读取 Excel 文件中的名称
excel_data = pd.read_excel(excel_file_path)
excel_names = excel_data['name'].tolist()  # 假设 Excel 中的列名为 'name'

# 获取目录下的文件名
file_names = os.listdir(directory_path)

# 打印不匹配的文件名
# 提取第一个 '-' 前面的字符
for file_name in file_names:
    if '-' in file_name:
        # 获取第一个 '-' 前面的字符
        base_name = file_name.split('-')[0]
        # print(f"文件名: {file_name}, 提取的字符: {base_name}")
        if base_name not in excel_names:
           print(f"不匹配的文件名: {base_name}")

#%% 批量将ipynb文件转换为py文件
import os
import subprocess

# 指定你的目标文件夹路径
folder = r'F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\2.pytorch深度学习和R代码总结版\深度学习代码总结最新版\2d和3d图像分割\3d swinunetr'

for root, dirs, files in os.walk(folder):
    for file in files:
        if file.endswith(".ipynb"):
            ipynb_path = os.path.join(root, file)
            py_path = os.path.splitext(ipynb_path)[0] + ".py"
            # 使用 nbconvert 转换
            cmd = [
                "jupyter", "nbconvert", "--to", "script", ipynb_path,
                "--output", os.path.splitext(file)[0]
            ]
            print(f"Converting: {ipynb_path}")
            subprocess.run(cmd, cwd=root)

#%%D:\2020-2023HCC\234image  路径下为3个文件夹ap pp hbp
# 得到每个文件夹下的文件名，分别保存到excel中的3列，给出代码

import os
import pandas as pd

# 定义图像文件夹路径
base_folder = r"N:\肝脏MRI数据集\HCC-TACE联合靶免\修正后图像\379HCC\image"
subfolders = ['ap', 'dwi','t1', 't2']
output_excel = r"N:\肝脏MRI数据集\HCC-TACE联合靶免\修正后图像\379HCC\image\序列文件名.xlsx"

# 初始化一个空的 DataFrame
df = pd.DataFrame()

# 遍历每个子文件夹，获取文件名并添加到 DataFrame 中
for subfolder in subfolders:
    folder_path = os.path.join(base_folder, subfolder)
    if os.path.exists(folder_path):
        files = [f for f in os.listdir(folder_path) if f.endswith('.nii.gz')]
        df[subfolder] = pd.Series(files)

# 将 DataFrame 保存为 Excel 文件
df.to_excel(output_excel, index=False)
print(f"File names saved to {output_excel}")

#%% 把路径AP下的文件夹名批量提取出来，保存到excel文件夹中
import os
import pandas as pd
path = r"K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\nii_output"

# 获取所有文件夹名
folders = [f for f in os.listdir(path) if os.path.isdir(os.path.join(path, f))]

 # 将文件夹名保存到DataFrame中并导出到Excel文件
df = pd.DataFrame({'Folder Name': folders})
df.to_excel(r'K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\nii_output\1.xlsx', index=False)

#%%把路径ap_data下的文件名批量提取出来，保存到excel文件夹中
import os
import pandas as pd
 # 设置目录路径
folder_path = r"K:\肝脏MRI数据集\HCC-EOBMRI\598HCC\image\t2"
excel_file_name = r"K:\肝脏MRI数据集\HCC-EOBMRI\598HCC\image\t2.xlsx"

 # 获取目录下的文件名并保存到Excel文件中
file_names = []
for file in os.listdir(folder_path):
    if os.path.isfile(os.path.join(folder_path, file)):
        file_names.append(file)
df = pd.DataFrame({'File Name': file_names})
new_excel_path = os.path.join(folder_path, excel_file_name)

# 替换路径
df.to_excel(new_excel_path, index=False)
# df.to_excel(r'K:\2020-2023HCC\579hcc\HCC1-344\新增2023HCC\image\ap\ap.xlsx', index=False)
print('Done.')


#%%#把I:\1.HCC-VETC\datasets\all-HCC-nantong\new_folder  下的文件中，如果文件名中含"AP"字样的文件剪切到新的文件夹
# ，创建文件夹为ap，把如果文件名中含"PP"字样的文件剪切到新的文件夹，创建文件夹为pp，
# 如果文件名中含"DP"字样的文件剪切到新的文件夹，创建文件夹为hbp，给出代码
import os
import shutil
root_dir = r"K:\肝脏MRI数据集\需要补充的T1-T2-DWI-ADC患者\nii_output"

# 定义序列类型和对应的文件夹名（包括大小写变体）
sequence_mapping = {
    'ap': ['ap', 'AP', 'Ap', 'aP','Arterial'],
    'pp': ['pp', 'PP', 'Pp', 'pP','Portal','portal'],
    'hbp': ['hbp', 'HBP', 'Hbp','20min','20-min'],
    'dwi': ['dwi', 'DWI', 'Dwi', 'dWi', 'dwI', 'DwI', 'dWI'],
    'adc': ['adc', 'ADC', 'Adc', 'aDc', 'adC', 'AdC', 'aDC'],
    't2': ['t2', 'T2', 't2wi', 'T2WI', 'T2wi', 't2WI', 't2Wi', 'T2Wi'],
    't1': ['t1', 'T1', 't1wi', 'T1WI', 'T1wi', 't1WI', 't1Wi', 'T1Wi']
}

# Create the subdirectories
for subdir in sequence_mapping.keys():
    new_dir = os.path.join(root_dir, subdir)
    if not os.path.exists(new_dir):
        os.makedirs(new_dir)

# Loop through all files in the root directory
for file in os.listdir(root_dir):
    if os.path.isfile(os.path.join(root_dir, file)):
        file_moved = False
        # 检查每个序列类型
        for folder_name, keywords in sequence_mapping.items():
            # 检查文件名是否包含任何关键词变体
            for keyword in keywords:
                if keyword in file:
                    shutil.move(os.path.join(root_dir, file), os.path.join(root_dir, folder_name, file))
                    print(f"移动文件 {file} 到 {folder_name} 文件夹")
                    file_moved = True
                    break
            if file_moved:
                break

        # 如果文件没有匹配任何序列类型，打印提示
        if not file_moved:
            print(f"文件 {file} 未匹配任何序列类型")

#%% 表格1和表格2根据相同的"姓名"进行合并,横向合并
#如果表格1中的姓名与表格2中的姓名相同，则复制表格1的DFS列的内容到表格2中的PFS列。            
import pandas as pd

#读取HCC生存资料随访表整理后.xlsx和HCC临床资料1-578完整版预后.xlsx中的数据
data1 = pd.read_excel(r"N:\肝脏MRI数据集\HCC-EOBMRI\王锦晶资料\884HCC临床预后资料0.xlsx")
data2 = pd.read_excel(r"N:\肝脏MRI数据集\HCC-EOBMRI\王锦晶资料\751ap_habitat_all_patients_features.xlsx")

#将姓名相同的行合并，左连接，以第一个数据框的name为基准，保留所有的行，横向合并
# merged_df = pd.merge(data1, data2, on='name', how='left')

#将姓名相同的行合并，只包含姓名相同的行
# merged_df = pd.merge(data1, data2, on='name', how='inner')#内连接，只保留两个数据框中都存在的行
merged_df = pd.merge(data1, data2, on='姓名', how='inner')#outer 外连接，保留两个数据框中所有的行

#保存结果到新的Excel文件
merged_df.to_excel(r"N:\肝脏MRI数据集\HCC-EOBMRI\王锦晶资料\884HCC临床组学资料.xlsx", index=False)

#%%将指定路径下的文件名中的大写字母改为小写字母
import os

path = r"N:\肝脏MRI数据集\HCC-EOBMRI\750HCC-suzhou\mask\t2"

# 遍历指定路径下的所有文件和文件夹
for root, dirs, files in os.walk(path):
    for file in files:
        # 构建文件的完整路径
        file_path = os.path.join(root, file)
        
        # 将文件名中的大写字母改为小写字母
        new_file_name = file.lower()
        
        # 构建新的文件完整路径
        new_file_path = os.path.join(root, new_file_name)
        
        # 重命名文件
        os.rename(file_path, new_file_path)
        print(f"文件 {file} 重命名为 {new_file_name}")

print("文件名中的大写字母已全部改为小写字母")

#%%替换指定路径下文件名中的 "vp" 字符为 "pp"
import os

# 指定路径
path = r"M:\苏大附二院NSCLC影像\nii_output\肺癌核对后的mask"

# 遍历路径下的所有文件
for filename in os.listdir(path):
    # 直接将文件名中的"_high_b"替换为空（无论是否包含）
    new_filename = filename.replace("_", "")
    
    # 如果替换后文件名有变化才进行重命名
    if new_filename != filename:
        src = os.path.join(path, filename)
        dst = os.path.join(path, new_filename)
        
        # 检查目标文件是否存在
        if not os.path.exists(dst):
            try:
                os.rename(src, dst)
                print(f"重命名: {src} -> {dst}")
            except Exception as e:
                print(f"无法重命名 {src}，错误: {e}")
        else:
            print(f"目标文件已存在，跳过重命名: {dst}")
# %%
