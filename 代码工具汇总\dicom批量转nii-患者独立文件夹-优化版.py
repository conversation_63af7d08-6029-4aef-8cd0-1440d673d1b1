#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICOM批量转NII.GZ - 患者独立文件夹版本
每个患者的转换结果保存在各自的患者文件夹内，患者文件夹相互独立

主要特点：
- 多线程并行转换，大幅提升速度
- 实时进度条显示
- 每个患者的nii.gz文件保存在患者自己的文件夹内
- 内存使用监控和优化
- 智能重试机制
- 支持复杂的目录结构（患者文件夹->序列文件夹或子文件夹->序列文件夹）

使用方法：
直接运行脚本，会提示输入路径，或者修改main函数中的base_path

作者：AI Assistant
日期：2025年
"""

import os
import sys
import time
import psutil
import logging
import traceback
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 第三方库
import dicom2nifti
import dicom2nifti.settings as settings
import pydicom
import numpy as np
from tqdm import tqdm

# 禁用所有验证以提高兼容性
settings.disable_validate_slice_increment()
settings.disable_validate_orientation() 
settings.disable_validate_multiframe_implicit()
settings.disable_validate_orthogonal()
settings.disable_validate_slicecount()

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, limit_gb: float = 8.0):
        self.limit_bytes = limit_gb * 1024 * 1024 * 1024
        self.process = psutil.Process()
        
    def check_memory(self) -> bool:
        """检查内存使用情况"""
        memory_info = self.process.memory_info()
        return memory_info.rss < self.limit_bytes
        
    def get_memory_usage_gb(self) -> float:
        """获取当前内存使用量(GB)"""
        return self.process.memory_info().rss / 1024 / 1024 / 1024

class PatientIndependentDicomConverter:
    """患者独立文件夹DICOM转换器"""
    
    def __init__(self, base_path: str, max_workers: int = 4, memory_limit_gb: float = 8.0):
        """
        初始化转换器

        Args:
            base_path: 包含患者文件夹的根目录
            max_workers: 并行工作线程数
            memory_limit_gb: 内存限制(GB)
        """
        self.base_path = Path(base_path)
        self.max_workers = max_workers
        self.memory_monitor = MemoryMonitor(memory_limit_gb)
        self.lock = threading.Lock()

        # 创建主要的输出目录
        self.nii_output_dir = self.base_path / "nii_output"
        self.nii_output_dir.mkdir(parents=True, exist_ok=True)

        # 创建dicom样本文件夹
        self.dicom_sample_dir = self.nii_output_dir / "dicom"
        self.dicom_sample_dir.mkdir(parents=True, exist_ok=True)

        # 统计信息
        self.stats = {
            'total': 0,
            'success': 0,
            'skipped': 0,
            'failed': 0,
            'no_dicom': 0
        }

        # 失败记录
        self.failed_sequences = []
        self.no_dicom_sequences = []

        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        # 创建日志目录（使用已经创建的nii_output_dir）
        log_dir = self.nii_output_dir / "conversion_logs"
        log_dir.mkdir(parents=True, exist_ok=True)

        # 配置日志
        log_file = log_dir / f"conversion_{time.strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(log_file, encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"患者独立文件夹转换器初始化完成")
        self.logger.info(f"根目录: {self.base_path}")
        self.logger.info(f"并行数: {self.max_workers}")
        self.logger.info(f"内存限制: {self.memory_monitor.limit_bytes / 1024 / 1024 / 1024:.1f}GB")
    
    def find_dicom_files(self, directory: Path, fast_mode: bool = True) -> List[Path]:
        """
        查找目录中的DICOM文件（优化版）
        """
        dicom_extensions = ['.dcm', '.DCM', '.dicom', '.DICOM']
        dicom_files = []
        
        # 使用rglob提高搜索效率
        for ext in dicom_extensions:
            dicom_files.extend(directory.glob(f'*{ext}'))
        
        if dicom_files and fast_mode:
            return dicom_files
        
        # 智能无扩展名文件检测
        if not dicom_files or not fast_mode:
            potential_files = [f for f in directory.iterdir() 
                             if f.is_file() and f.stat().st_size > 1024
                             and f.name not in ['StudyInfo.dat', 'DICOMDIR', 'VERSION', 'Thumbs.db']]
            
            # 限制检查数量，优先检查较大的文件
            potential_files.sort(key=lambda x: x.stat().st_size, reverse=True)
            potential_files = potential_files[:20]  # 只检查前20个最大的文件
            
            for file_path in potential_files:
                try:
                    with open(file_path, 'rb') as f:
                        header = f.read(256)
                        if b'DICM' in header or b'DICOM' in header:
                            dicom_files.append(file_path)
                            if fast_mode:
                                break
                except:
                    continue
        
        return dicom_files
    
    def get_sequence_info(self, dicom_file: Path) -> Tuple[str, str]:
        """获取序列信息（缓存优化版）"""
        try:
            # 使用更快的读取方式，只读取必要的标签
            ds = pydicom.dcmread(str(dicom_file), force=True, 
                                specific_tags=['SeriesNumber', 'SeriesDescription'])
            
            series_number = str(getattr(ds, 'SeriesNumber', 'Unknown'))
            series_description = getattr(ds, 'SeriesDescription', 'Unknown').strip()
            
            # 清理文件名
            series_description = "".join(c if c.isalnum() or c in ('-', '_', ' ') else '_' 
                                       for c in series_description)
            series_description = series_description.replace(' ', '_')
            
            return series_number, series_description
            
        except Exception as e:
            self.logger.warning(f"无法读取DICOM文件 {dicom_file}: {e}")
            return "Unknown", "Unknown"
    
    def _check_existing_file(self, output_file: Path) -> bool:
        """检查文件是否已存在且有效"""
        if not output_file.exists():
            return False
        
        try:
            file_size = output_file.stat().st_size
            if file_size < 1024:
                return False
            
            # 可选的nibabel验证
            try:
                import nibabel as nib
                img = nib.load(str(output_file))
                if len(img.shape) < 3:
                    return False
                return True
            except ImportError:
                return True  # 没有nibabel时，只检查文件大小
                
        except Exception:
            return False
    
    def detect_patient_sequences(self) -> List[Tuple[Path, str, str, Path]]:
        """
        检测患者目录结构
        返回: [(sequence_path, patient_name, sequence_id, patient_output_dir), ...]
        """
        conversion_list = []
        
        self.logger.info(f"开始分析患者目录结构: {self.base_path}")
        
        # 遍历患者文件夹
        for patient_dir in self.base_path.iterdir():
            if not patient_dir.is_dir():
                continue
                
            patient_name = patient_dir.name
            self.logger.info(f"分析患者: {patient_name}")
            
            # 在患者文件夹内查找序列
            patient_sequences = self._find_sequences_in_patient_dir(patient_dir, patient_name)
            conversion_list.extend(patient_sequences)
        
        self.logger.info(f"总共发现 {len(conversion_list)} 个需要转换的序列")
        return conversion_list
    
    def _find_sequences_in_patient_dir(self, patient_dir: Path, patient_name: str) -> List[Tuple[Path, str, str, Path]]:
        """在患者目录内查找序列"""
        sequences = []

        # 创建患者输出文件夹（使用已经创建的nii_output_dir）
        patient_output_dir = self.nii_output_dir / patient_name
        patient_output_dir.mkdir(parents=True, exist_ok=True)

        # 标记是否已经为该患者保存了样本DICOM文件
        sample_dicom_saved = False

        # 使用os.walk遍历患者目录下的所有子目录
        for root, dirs, files in os.walk(patient_dir):
            root_path = Path(root)

            # 检查当前目录是否包含DICOM文件
            dicom_files = self.find_dicom_files(root_path, fast_mode=True)

            if dicom_files:
                # 为该患者保存一个样本DICOM文件（只保存一次）
                if not sample_dicom_saved:
                    self._save_sample_dicom(dicom_files[0], patient_name, self.dicom_sample_dir)
                    sample_dicom_saved = True

                # 计算相对于患者目录的路径作为序列标识符
                rel_path = root_path.relative_to(patient_dir)
                if rel_path == Path('.'):  # 直接在患者目录下
                    sequence_id = "dicom"
                else:
                    sequence_id = '_'.join(rel_path.parts)

                sequences.append((root_path, patient_name, sequence_id, patient_output_dir))

        return sequences

    def _save_sample_dicom(self, dicom_file: Path, patient_name: str, dicom_sample_dir: Path):
        """为患者保存一个样本DICOM文件"""
        try:
            import shutil

            # 生成样本DICOM文件名
            sample_dicom_name = f"{patient_name}.dcm"
            sample_dicom_path = dicom_sample_dir / sample_dicom_name

            # 如果样本文件已存在，跳过
            if sample_dicom_path.exists():
                return

            # 复制DICOM文件
            shutil.copy2(dicom_file, sample_dicom_path)
            self.logger.info(f"已保存患者 {patient_name} 的样本DICOM文件: {sample_dicom_name}")

        except Exception as e:
            self.logger.warning(f"保存患者 {patient_name} 的样本DICOM文件失败: {e}")

    def convert_sequence_safe(self, args: Tuple[Path, str, str, Path]) -> Dict[str, Any]:
        """
        线程安全的序列转换函数

        Args:
            args: (sequence_path, patient_name, sequence_id, patient_output_dir)

        Returns:
            转换结果字典
        """
        sequence_path, patient_name, sequence_id, patient_output_dir = args

        try:
            # 内存检查
            if not self.memory_monitor.check_memory():
                return {
                    'success': False,
                    'patient_name': patient_name,
                    'sequence_id': sequence_id,
                    'error': f'内存使用超限: {self.memory_monitor.get_memory_usage_gb():.1f}GB'
                }

            # 生成输出文件名
            if sequence_id == "dicom":
                dicom_files = self.find_dicom_files(sequence_path, fast_mode=False)
                if not dicom_files:
                    return {
                        'success': False,
                        'patient_name': patient_name,
                        'sequence_id': sequence_id,
                        'error': '未找到DICOM文件'
                    }
                series_number, series_description = self.get_sequence_info(dicom_files[0])
                output_filename = f"{patient_name}-{series_number}_{series_description}.nii.gz"
            else:
                output_filename = f"{patient_name}-{sequence_id}.nii.gz"

            # 输出文件保存在患者自己的文件夹内
            output_file = patient_output_dir / output_filename

            # 检查是否已存在
            if self._check_existing_file(output_file):
                return {
                    'success': True,
                    'skipped': True,
                    'patient_name': patient_name,
                    'sequence_id': sequence_id,
                    'output_file': str(output_file)
                }

            # 处理重复文件名
            counter = 1
            while output_file.exists():
                name_part = output_filename.replace('.nii.gz', '')
                output_file = patient_output_dir / f"{name_part}_{counter}.nii.gz"
                counter += 1

            # 执行转换
            success = self._convert_with_retry(sequence_path, output_file)

            return {
                'success': success,
                'skipped': False,
                'patient_name': patient_name,
                'sequence_id': sequence_id,
                'output_file': str(output_file),
                'sequence_path': str(sequence_path)
            }

        except Exception as e:
            return {
                'success': False,
                'patient_name': patient_name,
                'sequence_id': sequence_id,
                'error': str(e),
                'sequence_path': str(sequence_path)
            }

    def _convert_with_retry(self, sequence_path: Path, output_file: Path, max_retries: int = 2) -> bool:
        """带重试的转换函数"""

        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.logger.info(f"重试转换 {sequence_path} (第{attempt+1}次)")

                # 尝试多种转换方法
                conversion_methods = [
                    self._try_standard_conversion,
                    self._try_robust_conversion,
                    self._try_size_filtered_conversion
                ]

                for method in conversion_methods:
                    try:
                        if method(sequence_path, output_file):
                            return True
                    except Exception as e:
                        continue

                # 如果是最后一次尝试，记录详细错误
                if attempt == max_retries - 1:
                    self.logger.error(f"所有转换方法都失败: {sequence_path}")

            except Exception as e:
                if attempt == max_retries - 1:
                    self.logger.error(f"转换失败 {sequence_path}: {e}")

        return False

    def _try_standard_conversion(self, sequence_path: Path, output_file: Path) -> bool:
        """标准转换方法"""
        dicom2nifti.dicom_series_to_nifti(
            str(sequence_path),
            str(output_file),
            reorient_nifti=False
        )
        return True

    def _try_robust_conversion(self, sequence_path: Path, output_file: Path) -> bool:
        """强制转换方法"""
        try:
            # 重新禁用所有验证
            settings.disable_validate_slice_increment()
            settings.disable_validate_orientation()
            settings.disable_validate_multiframe_implicit()
            settings.disable_validate_orthogonal()
            settings.disable_validate_slicecount()

            dicom2nifti.dicom_series_to_nifti(
                str(sequence_path),
                str(output_file),
                reorient_nifti=False
            )
            return True

        except Exception as e:
            error_str = str(e)
            if "IMAGE_ORIENTATION_INCONSISTENT" in error_str:
                try:
                    import dicom2nifti.convert_generic as convert_generic
                    convert_generic.dicom_series_to_nifti(
                        str(sequence_path),
                        str(output_file),
                        reorient_nifti=False
                    )
                    return True
                except:
                    pass
            return False

    def _try_size_filtered_conversion(self, sequence_path: Path, output_file: Path) -> bool:
        """尺寸过滤转换方法（简化版）"""
        import shutil
        import tempfile

        try:
            dicom_files = self.find_dicom_files(sequence_path, fast_mode=False)
            if not dicom_files:
                return False

            # 快速检查文件一致性
            valid_files = []
            target_size = None

            for dcm_file in dicom_files[:10]:  # 只检查前10个文件以提高速度
                try:
                    ds = pydicom.dcmread(str(dcm_file), force=True,
                                       specific_tags=['Rows', 'Columns'])
                    rows = getattr(ds, 'Rows', 0)
                    cols = getattr(ds, 'Columns', 0)

                    if rows > 0 and cols > 0:
                        current_size = (rows, cols)
                        if target_size is None:
                            target_size = current_size
                        if current_size == target_size:
                            valid_files.append(dcm_file)
                except:
                    continue

            if len(valid_files) < 3:
                return False

            # 创建临时目录并复制文件
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)

                for i, dcm_file in enumerate(valid_files):
                    temp_file = temp_path / f"{i:04d}.dcm"
                    shutil.copy2(dcm_file, temp_file)

                # 尝试转换
                dicom2nifti.dicom_series_to_nifti(
                    str(temp_path),
                    str(output_file),
                    reorient_nifti=False
                )

            return True

        except Exception as e:
            return False

    def update_stats(self, result: Dict[str, Any]):
        """更新统计信息（线程安全）"""
        with self.lock:
            if result['success']:
                if result.get('skipped', False):
                    self.stats['skipped'] += 1
                self.stats['success'] += 1
            elif 'error' in result and '未找到DICOM文件' in result['error']:
                self.stats['no_dicom'] += 1
                self.no_dicom_sequences.append(result)
            else:
                self.stats['failed'] += 1
                self.failed_sequences.append(result)

    def run_conversion(self):
        """执行批量转换（并行版）"""
        self.logger.info("="*60)
        self.logger.info("开始DICOM批量转换（nii_output文件夹版）")
        self.logger.info(f"输入路径: {self.base_path}")
        self.logger.info(f"输出路径: {self.nii_output_dir}")
        self.logger.info(f"nii.gz文件: 保存在nii_output/患者名/文件夹内")
        self.logger.info(f"样本DICOM: 保存在nii_output/dicom/文件夹内")
        self.logger.info(f"并行数: {self.max_workers}")
        self.logger.info(f"内存限制: {self.memory_monitor.limit_bytes / 1024 / 1024 / 1024:.1f}GB")
        self.logger.info("="*60)

        # 检测患者目录结构
        conversion_list = self.detect_patient_sequences()

        if not conversion_list:
            self.logger.warning("未找到需要转换的DICOM序列")
            return

        self.stats['total'] = len(conversion_list)

        # 创建进度条
        with tqdm(total=len(conversion_list), desc="转换进度",
                 unit="序列", dynamic_ncols=True) as pbar:

            # 并行执行转换
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_args = {
                    executor.submit(self.convert_sequence_safe, args): args
                    for args in conversion_list
                }

                # 处理完成的任务
                for future in as_completed(future_to_args):
                    result = future.result()
                    self.update_stats(result)

                    # 更新进度条
                    status = "✅" if result['success'] else "❌"
                    if result['success'] and result.get('skipped'):
                        status = "⏭️"

                    pbar.set_postfix({
                        '成功': self.stats['success'],
                        '失败': self.stats['failed'],
                        '跳过': self.stats['skipped'],
                        '内存': f"{self.memory_monitor.get_memory_usage_gb():.1f}GB"
                    })
                    pbar.update(1)

        # 输出统计结果
        self._print_summary()

    def _print_summary(self):
        """打印转换摘要"""
        total = self.stats['total']
        success = self.stats['success']
        failed = self.stats['failed']
        skipped = self.stats['skipped']
        no_dicom = self.stats['no_dicom']

        self.logger.info("="*60)
        self.logger.info("转换完成!")
        self.logger.info(f"总序列数: {total}")
        self.logger.info(f"成功转换: {success - skipped}")
        self.logger.info(f"跳过已存在: {skipped}")
        self.logger.info(f"总成功数: {success}")
        self.logger.info(f"失败数量: {failed}")
        self.logger.info(f"无DICOM文件: {no_dicom}")

        valid_total = total - no_dicom
        if valid_total > 0:
            success_rate = success / valid_total * 100
            self.logger.info(f"成功率: {success_rate:.1f}%")

        # 详细失败信息
        if self.failed_sequences:
            self.logger.info("\n❌ 转换失败的序列:")
            for i, failed in enumerate(self.failed_sequences[:10], 1):  # 只显示前10个
                self.logger.info(f"{i}. {failed['patient_name']}-{failed['sequence_id']}")
                if 'error' in failed:
                    self.logger.info(f"   错误: {failed['error']}")

        # 性能统计
        memory_usage = self.memory_monitor.get_memory_usage_gb()
        self.logger.info(f"\n📊 性能统计:")
        self.logger.info(f"最终内存使用: {memory_usage:.1f}GB")
        self.logger.info("="*60)

def main():
    """主函数"""
    print("DICOM批量转换工具 - nii_output文件夹版（含样本DICOM）")
    print("="*60)
    print("特点：")
    print("1. 在base_path下创建nii_output文件夹，按患者分类保存nii.gz文件")
    print("2. 同时为每个患者保留一个样本DICOM文件在nii_output/dicom/文件夹中")
    print("="*60)

    # ==================== 在这里修改路径和参数 ====================
    base_path = r"M:\新增10附二院"

    # 配置参数 - 可以直接在这里修改
    max_workers = 8        # 并行线程数
    memory_limit = 8.0     # 内存限制GB
    # ========================================================

    print(f"使用路径: {base_path}")

    if not Path(base_path).exists():
        print(f"❌ 路径不存在: {base_path}")
        print("请在main函数中修改base_path变量")
        return

    print(f"\n开始转换...")
    print(f"输入路径: {base_path}")
    print(f"输出路径: {base_path}\\nii_output")
    print(f"并行数: {max_workers}")
    print(f"内存限制: {memory_limit:.1f}GB")
    print(f"nii.gz文件: 保存在nii_output/患者名/文件夹内")
    print(f"样本DICOM: 保存在nii_output/dicom/文件夹内")
    print("="*60)

    # 创建转换器并执行
    converter = PatientIndependentDicomConverter(
        base_path=base_path,
        max_workers=max_workers,
        memory_limit_gb=memory_limit
    )

    try:
        start_time = time.time()
        converter.run_conversion()
        end_time = time.time()

        print(f"\n⏱️ 总耗时: {end_time - start_time:.1f}秒")
        print("✅ 转换完成！")
        print(f"📁 nii.gz文件: {base_path}\\nii_output\\患者名\\")
        print(f"📁 样本DICOM: {base_path}\\nii_output\\dicom\\")
        print(f"📁 转换日志: {base_path}\\nii_output\\conversion_logs\\")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断转换")
    except Exception as e:
        print(f"\n❌ 转换过程中发生错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
