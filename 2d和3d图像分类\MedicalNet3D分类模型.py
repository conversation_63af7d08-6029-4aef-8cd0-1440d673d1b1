"""
Med3D: 3D图像分类模型 (基于MedicalNet预训练权重的迁移学习)
支持从Excel导入标签,训练后保存预测概率和标签到Excel
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import nibabel as nib
from monai.transforms import (
    Compose, LoadImaged, EnsureChannelFirstd, Resized,
    ScaleIntensityRanged, EnsureTyped, RandRotate90d,
    RandFlipd, RandGaussianNoised
)
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# ============================================================================
# 配置参数
# ============================================================================
class Config:
    # 数据路径
    train_data_dir = r"N:\肝脏MRI数据集\HCC-EOBMRI\750HCC-suzhou\tumor\ap"  # 训练数据文件夹
    test_data_dir = r"N:\肝脏MRI数据集\HCC-EOBMRI\234HCC-nantong\tumor\ap"   # 测试数据文件夹
    train_label_excel = r"N:\肝脏MRI数据集\HCC-EOBMRI\张孟溦-小HCC数据\train.xlsx"  # 训练集标签Excel
    test_label_excel = r"N:\肝脏MRI数据集\HCC-EOBMRI\张孟溦-小HCC数据\test.xlsx"   # 测试集标签Excel

    # 标签列名 (Excel中的列名)
    label_name = 'MVI'  # 修改为您Excel中的标签列名,如 'PHCC', 'VETC' 等
    name_column = '姓名'  # Excel中文件名对应的列名

    # 预训练模型路径
    pretrain_path = r'F:\2. python深度学习课程\1.Pytorch深度学习代码汇总\2.pytorch深度学习和R代码总结版\深度学习代码总结最新版\2d和3d图像分类\3d分类Github项目\MedicalNet3D预训练\pretrain\resnet_50_23dataset.pth'

    # 模型参数
    model_depth = 50  # ResNet深度: 10, 18, 34, 50, 101, 152, 200
    resnet_shortcut = 'B'  # 'A' or 'B'
    num_classes = 2  # 分类类别数

    # 输入尺寸 (根据您的数据调整)
    input_D = 64  # 深度 (z轴)
    input_H = 64  # 高度 (y轴)
    input_W = 64  # 宽度 (x轴)

    # 训练参数
    batch_size = 4
    num_epochs = 100
    learning_rate = 0.0001
    weight_decay = 1e-4

    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 结果保存路径
    save_dir = './results_med3d'
    os.makedirs(save_dir, exist_ok=True)
    best_model_path = os.path.join(save_dir, 'best_model.pth')
    final_model_path = os.path.join(save_dir, 'final_model.pth')
    train_results_path = os.path.join(save_dir, 'train_results.xlsx')
    test_results_path = os.path.join(save_dir, 'test_results.xlsx')

config = Config()

# ============================================================================
# 数据集定义
# ============================================================================
def create_dataset(data_dir, label_excel, label_name, name_column='name'):
    """
    创建数据集字典列表
    从Excel中读取标签,匹配nii.gz文件
    """
    # 读取Excel文件
    df = pd.read_excel(label_excel)

    # 获取图像文件列表
    image_files = [f for f in os.listdir(data_dir) if f.endswith('.nii.gz')]

    # 创建包含图像路径和标签的字典列表
    data = []
    for img_file in image_files:
        # 从文件名中提取ID (去除.nii.gz后缀)
        file_id = img_file.replace('.nii.gz', '').replace('.nii', '')

        # 提取第一个'-'前面的名字用于匹配
        name_only = file_id.split('-')[0] if '-' in file_id else file_id

        # 在Excel中查找对应的标签
        label_row = df[df[name_column].astype(str) == name_only]

        if not label_row.empty:
            label = label_row[label_name].values[0]
            data.append({
                'image': os.path.join(data_dir, img_file),
                'label': int(label),
                'name': file_id
            })
        else:
            print(f"Warning: No label found for {file_id} (searched: {name_only})")

    print(f"Found {len(data)} samples with labels")
    return data

class Med3DDataset(Dataset):
    """Med3D数据集"""
    def __init__(self, data_list, transform=None):
        self.data_list = data_list
        self.transform = transform

    def __len__(self):
        return len(self.data_list)

    def __getitem__(self, idx):
        data_dict = self.data_list[idx].copy()

        if self.transform:
            data_dict = self.transform(data_dict)

        return {
            'image': data_dict['image'],
            'label': torch.tensor(data_dict['label'], dtype=torch.long),
            'name': data_dict['name']
        }

# 数据增强和预处理
train_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=(config.input_D, config.input_H, config.input_W)),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=300, b_min=0.0, b_max=1.0, clip=True),
    RandRotate90d(keys=["image"], prob=0.5, spatial_axes=(1, 2)),
    RandFlipd(keys=["image"], prob=0.5, spatial_axis=0),
    RandGaussianNoised(keys=["image"], prob=0.3, mean=0.0, std=0.1),
    EnsureTyped(keys=["image"])
])

test_transforms = Compose([
    LoadImaged(keys=["image"]),
    EnsureChannelFirstd(keys=["image"]),
    Resized(keys=["image"], spatial_size=(config.input_D, config.input_H, config.input_W)),
    ScaleIntensityRanged(keys=["image"], a_min=-200, a_max=300, b_min=0.0, b_max=1.0, clip=True),
    EnsureTyped(keys=["image"])
])

# ============================================================================
# 模型定义 (基于MedicalNet ResNet)
# ============================================================================
def conv3x3x3(in_planes, out_planes, stride=1, dilation=1):
    return nn.Conv3d(in_planes, out_planes, kernel_size=3, dilation=dilation,
                     stride=stride, padding=dilation, bias=False)

class BasicBlock(nn.Module):
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, dilation=1, downsample=None):
        super(BasicBlock, self).__init__()
        self.conv1 = conv3x3x3(inplanes, planes, stride=stride, dilation=dilation)
        self.bn1 = nn.BatchNorm3d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = conv3x3x3(planes, planes, dilation=dilation)
        self.bn2 = nn.BatchNorm3d(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        out = self.conv2(out)
        out = self.bn2(out)
        if self.downsample is not None:
            residual = self.downsample(x)
        out += residual
        out = self.relu(out)
        return out

class Bottleneck(nn.Module):
    expansion = 4

    def __init__(self, inplanes, planes, stride=1, dilation=1, downsample=None):
        super(Bottleneck, self).__init__()
        self.conv1 = nn.Conv3d(inplanes, planes, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm3d(planes)
        self.conv2 = nn.Conv3d(planes, planes, kernel_size=3, stride=stride,
                               dilation=dilation, padding=dilation, bias=False)
        self.bn2 = nn.BatchNorm3d(planes)
        self.conv3 = nn.Conv3d(planes, planes * 4, kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm3d(planes * 4)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)
        out = self.conv3(out)
        out = self.bn3(out)
        if self.downsample is not None:
            residual = self.downsample(x)
        out += residual
        out = self.relu(out)
        return out

class ResNet3D(nn.Module):
    """3D ResNet for classification"""
    def __init__(self, block, layers, num_classes=2, shortcut_type='B'):
        super(ResNet3D, self).__init__()
        self.inplanes = 64

        self.conv1 = nn.Conv3d(1, 64, kernel_size=7, stride=(2, 2, 2),
                               padding=(3, 3, 3), bias=False)
        self.bn1 = nn.BatchNorm3d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=(3, 3, 3), stride=2, padding=1)

        self.layer1 = self._make_layer(block, 64, layers[0], shortcut_type)
        self.layer2 = self._make_layer(block, 128, layers[1], shortcut_type, stride=2)
        self.layer3 = self._make_layer(block, 256, layers[2], shortcut_type, stride=1, dilation=2)
        self.layer4 = self._make_layer(block, 512, layers[3], shortcut_type, stride=1, dilation=4)

        self.avgpool = nn.AdaptiveAvgPool3d((1, 1, 1))
        self.fc = nn.Linear(512 * block.expansion, num_classes)

        # 初始化权重
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def _make_layer(self, block, planes, blocks, shortcut_type, stride=1, dilation=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv3d(self.inplanes, planes * block.expansion, kernel_size=1,
                         stride=stride, bias=False),
                nn.BatchNorm3d(planes * block.expansion))

        layers = []
        layers.append(block(self.inplanes, planes, stride=stride,
                           dilation=dilation, downsample=downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes, dilation=dilation))

        return nn.Sequential(*layers)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)

        return x

def resnet10(num_classes=2, shortcut_type='B'):
    return ResNet3D(BasicBlock, [1, 1, 1, 1], num_classes, shortcut_type)

def resnet18(num_classes=2, shortcut_type='A'):
    return ResNet3D(BasicBlock, [2, 2, 2, 2], num_classes, shortcut_type)

def resnet34(num_classes=2, shortcut_type='A'):
    return ResNet3D(BasicBlock, [3, 4, 6, 3], num_classes, shortcut_type)

def resnet50(num_classes=2, shortcut_type='B'):
    return ResNet3D(Bottleneck, [3, 4, 6, 3], num_classes, shortcut_type)

def resnet101(num_classes=2, shortcut_type='B'):
    return ResNet3D(Bottleneck, [3, 4, 23, 3], num_classes, shortcut_type)

def resnet152(num_classes=2, shortcut_type='B'):
    return ResNet3D(Bottleneck, [3, 8, 36, 3], num_classes, shortcut_type)

def resnet200(num_classes=2, shortcut_type='B'):
    return ResNet3D(Bottleneck, [3, 24, 36, 3], num_classes, shortcut_type)

# ============================================================================
# 加载预训练权重
# ============================================================================
def load_pretrained_weights(model, pretrain_path):
    """加载MedicalNet预训练权重"""
    print(f'Loading pretrained model from {pretrain_path}')

    pretrain = torch.load(pretrain_path, map_location='cpu')
    pretrain_dict = pretrain['state_dict']

    # 获取模型的state_dict
    model_dict = model.state_dict()

    # 过滤掉不匹配的键 (主要是fc层和conv_seg层)
    pretrain_dict = {k.replace('module.', ''): v for k, v in pretrain_dict.items()
                     if k.replace('module.', '') in model_dict and
                     'fc' not in k and 'conv_seg' not in k}

    # 更新模型权重
    model_dict.update(pretrain_dict)
    model.load_state_dict(model_dict)

    print(f'Loaded {len(pretrain_dict)} pretrained layers')
    return model

# ============================================================================
# 训练和评估函数
# ============================================================================
def train_epoch(model, dataloader, criterion, optimizer, device):
    """训练一个epoch"""
    model.train()
    running_loss = 0.0
    all_preds = []
    all_labels = []

    progress_bar = tqdm(dataloader, desc='Training')
    for batch in progress_bar:
        images = batch['image'].to(device)
        labels = batch['label'].to(device)

        optimizer.zero_grad()
        outputs = model(images)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()

        _, preds = torch.max(outputs, 1)
        all_preds.extend(preds.cpu().numpy())
        all_labels.extend(labels.cpu().numpy())

        progress_bar.set_postfix({'loss': loss.item()})

    epoch_loss = running_loss / len(dataloader)
    epoch_acc = accuracy_score(all_labels, all_preds)

    return epoch_loss, epoch_acc

def evaluate(model, dataloader, criterion, device):
    """评估模型"""
    model.eval()
    running_loss = 0.0
    all_preds = []
    all_labels = []
    all_probs = []
    all_names = []

    with torch.no_grad():
        for batch in tqdm(dataloader, desc='Evaluating'):
            images = batch['image'].to(device)
            labels = batch['label'].to(device)
            names = batch['name']

            outputs = model(images)
            loss = criterion(outputs, labels)

            running_loss += loss.item()

            probs = torch.softmax(outputs, dim=1)
            _, preds = torch.max(outputs, 1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
            all_names.extend(names)

    epoch_loss = running_loss / len(dataloader)
    epoch_acc = accuracy_score(all_labels, all_preds)

    return epoch_loss, epoch_acc, all_preds, all_labels, all_probs, all_names

def save_results_to_excel(names, labels, preds, probs, save_path, num_classes):
    """保存预测结果到Excel"""
    results = {
        'name': names,
        'True_label': labels,
        'Predict_label': preds,
    }

    # 添加每个类别的概率
    for i in range(num_classes):
        results[f'Prob_class_{i}'] = [prob[i] for prob in probs]

    # 最大预测概率
    results['Max_probability'] = [max(prob) for prob in probs]

    df = pd.DataFrame(results)
    df.to_excel(save_path, index=False)
    print(f'Results saved to {save_path}')

# ============================================================================
# 主训练流程
# ============================================================================
def main():
    print("=" * 80)
    print("Med3D: 3D Image Classification with Pretrained MedicalNet")
    print("=" * 80)

    # 创建数据集
    print("\nLoading datasets...")
    train_data = create_dataset(config.train_data_dir, config.train_label_excel,
                                config.label_name, config.name_column)
    test_data = create_dataset(config.test_data_dir, config.test_label_excel,
                               config.label_name, config.name_column)

    # 创建数据加载器
    train_dataset = Med3DDataset(train_data, transform=train_transforms)
    test_dataset = Med3DDataset(test_data, transform=test_transforms)

    train_loader = DataLoader(train_dataset, batch_size=config.batch_size,
                             shuffle=True, num_workers=2, pin_memory=True)
    test_loader = DataLoader(test_dataset, batch_size=config.batch_size,
                            shuffle=False, num_workers=2, pin_memory=True)

    print(f"Train samples: {len(train_dataset)}")
    print(f"Test samples: {len(test_dataset)}")

    # 创建模型
    print("\nBuilding model...")
    if config.model_depth == 10:
        model = resnet10(num_classes=config.num_classes, shortcut_type=config.resnet_shortcut)
    elif config.model_depth == 18:
        model = resnet18(num_classes=config.num_classes, shortcut_type=config.resnet_shortcut)
    elif config.model_depth == 34:
        model = resnet34(num_classes=config.num_classes, shortcut_type=config.resnet_shortcut)
    elif config.model_depth == 50:
        model = resnet50(num_classes=config.num_classes, shortcut_type=config.resnet_shortcut)
    elif config.model_depth == 101:
        model = resnet101(num_classes=config.num_classes, shortcut_type=config.resnet_shortcut)
    elif config.model_depth == 152:
        model = resnet152(num_classes=config.num_classes, shortcut_type=config.resnet_shortcut)
    elif config.model_depth == 200:
        model = resnet200(num_classes=config.num_classes, shortcut_type=config.resnet_shortcut)
    else:
        raise ValueError(f"Unsupported model_depth: {config.model_depth}")

    # 加载预训练权重
    if os.path.exists(config.pretrain_path):
        model = load_pretrained_weights(model, config.pretrain_path)
    else:
        print(f"Warning: Pretrain path not found: {config.pretrain_path}")
        print("Training from scratch...")

    model = model.to(config.device)

    # 计算类别权重以处理类别不平衡
    train_labels = [item['label'] for item in train_data]
    class_counts = np.bincount(train_labels)
    class_weights = 1.0 / class_counts
    class_weights = class_weights / class_weights.sum() * len(class_counts)
    class_weights = torch.FloatTensor(class_weights).to(config.device)
    print(f"Class distribution: {class_counts}")
    print(f"Class weights: {class_weights}")

    # 定义损失函数和优化器（使用类别权重）
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    optimizer = optim.Adam(model.parameters(), lr=config.learning_rate,
                          weight_decay=config.weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max',
                                                     patience=5, factor=0.5)

    # 训练循环
    print("\nStarting training...")
    best_acc = 0.0

    for epoch in range(config.num_epochs):
        print(f"\nEpoch [{epoch+1}/{config.num_epochs}]")

        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, config.device)
        print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")

        # 验证
        test_loss, test_acc, test_preds, test_labels, test_probs, test_names = \
            evaluate(model, test_loader, criterion, config.device)
        print(f"Test Loss: {test_loss:.4f}, Test Acc: {test_acc:.4f}")

        # 学习率调整
        scheduler.step(test_acc)

        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            torch.save(model.state_dict(), config.best_model_path)
            print(f"Best model saved with accuracy: {best_acc:.4f}")

    # 保存最终模型权重
    torch.save(model.state_dict(), config.final_model_path)
    print(f"\nFinal model saved to: {config.final_model_path}")

    # 加载最佳模型进行最终评估
    print("\n" + "=" * 80)
    print("Final Evaluation on Best Model")
    print("=" * 80)

    model.load_state_dict(torch.load(config.best_model_path))

    # 训练集评估
    _, train_acc, train_preds, train_labels, train_probs, train_names = \
        evaluate(model, train_loader, criterion, config.device)
    print(f"\nTrain Accuracy: {train_acc:.4f}")
    print("\nTrain Classification Report:")
    print(classification_report(train_labels, train_preds))

    # 保存训练集结果
    save_results_to_excel(train_names, train_labels, train_preds, train_probs,
                         config.train_results_path, config.num_classes)

    # 测试集评估
    _, test_acc, test_preds, test_labels, test_probs, test_names = \
        evaluate(model, test_loader, criterion, config.device)
    print(f"\nTest Accuracy: {test_acc:.4f}")
    print("\nTest Classification Report:")
    print(classification_report(test_labels, test_preds))

    # 计算AUC (二分类)
    if config.num_classes == 2:
        train_auc = roc_auc_score(train_labels, [prob[1] for prob in train_probs])
        test_auc = roc_auc_score(test_labels, [prob[1] for prob in test_probs])
        print(f"\nTrain AUC: {train_auc:.4f}")
        print(f"Test AUC: {test_auc:.4f}")

    # 保存测试集结果
    save_results_to_excel(test_names, test_labels, test_preds, test_probs,
                         config.test_results_path, config.num_classes)

    print("\n" + "=" * 80)
    print("Training completed!")
    print(f"Results saved in: {config.save_dir}")
    print(f"Best model: {config.best_model_path} (Best Acc: {best_acc:.4f})")
    print(f"Final model: {config.final_model_path}")
    print("=" * 80)

if __name__ == '__main__':
    main()
